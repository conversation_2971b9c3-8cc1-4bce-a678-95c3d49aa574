<?php

namespace App\Models;

use App\Enums\FavoriteType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlaceFavorite extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'place_id',
        'type',
    ];

    protected $casts = [
        'type' => FavoriteType::class,
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user that owns the favorite.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the place that is favorited.
     */
    public function place(): BelongsTo
    {
        return $this->belongsTo(Place::class);
    }
}
