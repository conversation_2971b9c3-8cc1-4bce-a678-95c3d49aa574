<?php

namespace App\Actions\Chat;

use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Enums\ChatMessageSender;
use Illuminate\Support\Facades\Log;

class ExecuteTransitionAction
{
    /**
     * Execute the agent transition
     *
     * @param ChatSession $chatSession The current chat session
     * @param Agent $fromAgent The agent transitioning from
     * @param Agent $toAgent The agent transitioning to
     * @param string $greetingMessage The transition greeting message
     * @param string $triggerType The type of trigger (automatic, manual, etc.)
     * @param ChatMessage|null $aiMessage The existing AI message to update (prevents duplication)
     * @param array|null $quickReplies Optional quick replies for the transition
     * @return array
     */
    public function execute(
        ChatSession $chatSession,
        Agent $fromAgent,
        Agent $toAgent,
        string $greetingMessage,
        string $triggerType,
        ?ChatMessage $aiMessage = null,
        ?array $quickReplies = null
    ): array {
      
         $aiMessage->update([
                // 'message_text' => $greetingMessage,
                'is_transition' => true,
                'from_agent_id' => $fromAgent->id,
                'to_agent_id' => $toAgent->id,
                'agent_id' => $toAgent->id,
                'quick_replies' => $quickReplies,
        ]);
    
        // Update chat session with new agent
        $chatSession->update([
            'agent_id' => $toAgent->id,
            'last_message_at' => now(),
        ]);

        Log::info('Agent transition executed', [
            'chat_session_id' => $chatSession->id,
            'from_agent_id' => $fromAgent->id,
            'to_agent_id' => $toAgent->id,
            'trigger_type' => $triggerType,
            'transition_message_id' => $aiMessage->id,
            'is_reusing_message' => $aiMessage ? true : false
        ]);

        return [
            'success' => true,
            'transition_message' => [
                'id' => $aiMessage->id,
                'message' => $greetingMessage,
                'from_agent' => [
                    'id' => $fromAgent->id,
                    'name' => $fromAgent->name
                ],
                'to_agent' => [
                    'id' => $toAgent->id,
                    'name' => $toAgent->name
                ],
                'quick_replies' => $quickReplies,
            ]
        ];
    }
}
