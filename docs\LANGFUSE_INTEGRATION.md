# Langfuse Integration Documentation

## Overview

This document describes the integration of Langfuse LLM observability and analytics platform into the rydo-backend Laravel application. Langfuse provides comprehensive tracking, monitoring, and analytics for LLM applications.

## What is Langfuse?

Langfuse is an open-source LLM engineering platform that provides:

- **LLM Observability**: Track and trace all LLM calls with full context
- **Cost & Usage Analytics**: Monitor token usage and costs across your application  
- **Prompt Management**: Version control and manage prompts centrally
- **Evaluation & Testing**: LLM-as-a-judge evaluations and user feedback collection
- **Performance Monitoring**: Latency, quality, and usage metrics
- **Session Tracking**: Group related interactions into sessions/conversations

## Features Implemented

### 1. **Automatic OpenAI Tracking**
- All OpenAI API calls are automatically tracked in Langfuse
- Includes prompt, response, token usage, and cost information
- Works with both regular and streaming responses

### 2. **Chat Session Tracking**
- Chat sessions are automatically created as Langfuse sessions
- Groups related messages and interactions
- Includes user and agent metadata

### 3. **Message-Level Tracing**
- Each chat message creates a trace in Langfuse
- Includes user input, agent response, and metadata
- Tracks transitions, recommendations, and other message types

### 4. **Cost and Usage Monitoring**
- Token usage and costs are tracked per message
- User-level analytics available
- Agent-level performance metrics

### 5. **Error Handling**
- Graceful fallback when Langfuse is unavailable
- Configurable error handling (fail silently or throw errors)
- Comprehensive logging of integration issues

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```env
# Langfuse Configuration
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=your_public_key_here
LANGFUSE_SECRET_KEY=your_secret_key_here
LANGFUSE_HOST=https://cloud.langfuse.com

# Optional Configuration
LANGFUSE_TIMEOUT=30
LANGFUSE_CONNECT_TIMEOUT=10
LANGFUSE_INCLUDE_USER_DATA=true
LANGFUSE_INCLUDE_METADATA=true
LANGFUSE_TRACE_IN_DEVELOPMENT=true
LANGFUSE_TRACE_IN_TESTING=false
LANGFUSE_MAX_CONTENT_LENGTH=10000
LANGFUSE_AUTO_CREATE_SESSIONS=true
LANGFUSE_INCLUDE_AGENT_INFO=true
LANGFUSE_INCLUDE_USER_INFO=true
LANGFUSE_FAIL_SILENTLY=true
LANGFUSE_LOG_ERRORS=true
LANGFUSE_LOG_LEVEL=warning
```

### Getting Langfuse Credentials

1. **Langfuse Cloud** (Recommended for getting started):
   - Sign up at [https://cloud.langfuse.com](https://cloud.langfuse.com)
   - Create a new project
   - Copy the Public Key and Secret Key from the project settings

2. **Self-Hosted Langfuse**:
   - Follow the [self-hosting guide](https://langfuse.com/docs/deployment/self-host)
   - Set `LANGFUSE_HOST` to your instance URL
   - Generate API keys in your instance

## Usage

### Automatic Tracking

Once configured, the integration works automatically:

1. **Chat Messages**: All chat interactions are automatically tracked
2. **OpenAI Calls**: LLM generations are captured with full context
3. **Sessions**: Chat sessions are grouped for conversation analysis
4. **User Analytics**: Per-user usage and cost tracking

### Manual Tracking

You can also manually track custom events:

```php
use App\Services\Langfuse\LangfuseTrackingService;

$langfuse = app(LangfuseTrackingService::class);

// Track user feedback
$langfuse->trackUserFeedback(
    $traceId,
    'user_rating',
    5,
    'Great response!',
    $generationId
);
```

## Monitoring and Analytics

### Langfuse Dashboard

Access your Langfuse dashboard to view:

- **Traces**: Individual conversation flows
- **Sessions**: Complete chat conversations
- **Analytics**: Usage, cost, and performance metrics
- **Users**: Per-user analytics and behavior
- **Models**: Model performance comparison

### Key Metrics

Monitor these important metrics:

1. **Cost per User**: Track spending by user
2. **Response Quality**: Monitor user feedback and ratings
3. **Latency**: Track response times
4. **Token Usage**: Monitor prompt and completion tokens
5. **Error Rates**: Track failed requests
6. **Conversation Length**: Average messages per session

## Troubleshooting

### Common Issues

1. **Langfuse not tracking**:
   - Check `LANGFUSE_ENABLED=true`
   - Verify API keys are correct
   - Check logs for error messages

2. **Performance impact**:
   - Enable batching for high-volume applications
   - Adjust `LANGFUSE_MAX_CONTENT_LENGTH` to reduce payload size
   - Set `LANGFUSE_FAIL_SILENTLY=true` for production

3. **Missing data**:
   - Ensure chat messages have proper relationships
   - Check that OpenAI calls include `chat_message_id`
   - Verify session creation is enabled

### Debugging

Enable debug logging:

```env
LANGFUSE_LOG_ERRORS=true
LANGFUSE_LOG_LEVEL=debug
```

Check Laravel logs for Langfuse-related messages:

```bash
php artisan pail --filter=langfuse
```

## Best Practices

### Production Deployment

1. **Error Handling**: Always set `LANGFUSE_FAIL_SILENTLY=true` in production
2. **Content Limits**: Set reasonable `LANGFUSE_MAX_CONTENT_LENGTH` to prevent large payloads
3. **Monitoring**: Set up alerts for Langfuse integration failures
4. **Batching**: Enable batching for high-volume applications

### Privacy and Security

1. **User Data**: Configure `LANGFUSE_INCLUDE_USER_DATA` based on privacy requirements
2. **Content Filtering**: Implement content filtering for sensitive information
3. **Access Control**: Restrict Langfuse dashboard access appropriately
4. **Data Retention**: Configure appropriate data retention policies

### Performance Optimization

1. **Async Processing**: Consider queuing Langfuse calls for high-volume applications
2. **Selective Tracking**: Disable tracking in testing environments
3. **Content Truncation**: Use content length limits to reduce payload size
4. **Batching**: Enable request batching for better performance

## Integration Points

The Langfuse integration hooks into these existing components:

- `OpenAIService`: Automatic tracking of all OpenAI API calls
- `TrackOpenAIUsageAction`: Enhanced with Langfuse tracing
- `ChatMessage` model: Automatic session and trace creation
- `ChatSession` model: Session-level analytics

## Future Enhancements

Potential improvements to consider:

1. **Prompt Management**: Integrate with Langfuse prompt management
2. **A/B Testing**: Use Langfuse for prompt A/B testing
3. **Evaluation Pipelines**: Implement automated quality evaluation
4. **Custom Dashboards**: Create application-specific analytics views
5. **Real-time Alerts**: Set up alerts for quality or cost thresholds
