<?php

namespace App\Console\Commands;

use App\Models\SystemSetting;
use App\Models\User;
use App\Services\TimeManipulationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RenewRegisteredPointsCommand extends Command
{
    protected $signature = 'points:renew-registered';
    protected $description = 'Renew points for registered users based on renewal interval';

    public function handle()
    {
        $now = TimeManipulationService::now();
        Log::info("Running RenewGuestPointsJob at {$now} (with offset: " . TimeManipulationService::getTimeOffsetFormatted() . ")");

        $renewalHours = SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_HOURS, 24);
        $renewalAmount = SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_AMOUNT, 200);

        $users = User::query()
            ->where('is_anonymous', false)
            ->whereDoesntHave('subscriptions', function ($query) use ($now) {
                $query->where('status', 'active')
                    ->orWhere('end_date', '>', $now);
            })
            ->where(function ($query) use ($now) {
                $query->whereNull('next_points_renewal_at')
                    ->orWhere('next_points_renewal_at', '<=', $now);
            })
            ->get();

        $count = 0;
        foreach ($users as $user) {
            try {
                $user->resetSomePoints(
                    $renewalAmount,
                    'Periodic points renewal for registered user'
                );

                $user->last_points_renewal_at = $now;
                $user->next_points_renewal_at = $now->addHours($renewalHours);
                $user->save();

                $count++;
            } catch (\Exception $e) {
                Log::error("Failed to renew points for user {$user->id}: " . $e->getMessage());
            }
        }

        $this->info("Successfully renewed points for {$count} registered users");
    }
}
