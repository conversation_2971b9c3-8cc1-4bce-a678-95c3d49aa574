<?php

namespace App\Filament\Resources\PromptResource\Pages;

use App\Filament\Resources\PromptResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;

class ViewPrompt extends ViewRecord
{
    protected static string $resource = PromptResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Model Configuration')
                    ->schema([
                        TextEntry::make('model_type')
                            ->badge()
                            ->color(fn ($state) => $state?->getColor()),
                        TextEntry::make('max_tokens')
                            ->numeric(),
                        TextEntry::make('temperature')
                            ->numeric(),
                        TextEntry::make('top_p')
                            ->numeric(),
                        TextEntry::make('frequency_penalty')
                            ->numeric(),
                        TextEntry::make('presence_penalty')
                            ->numeric(),
                    ])
                    ->columns(3),

                Section::make('Prompt Content')
                    ->schema([
                        TextEntry::make('content')
                            ->markdown()
                            ->columnSpanFull(),
                    ]),

                Section::make('Agents Using This Prompt')
                    ->schema([
                        TextEntry::make('agents.name')
                            ->label('Agent Names')
                            ->listWithLineBreaks()
                            ->state(function ($record) {
                                return $record->agents->pluck('name')->toArray();
                            }),
                    ])
                    ->collapsible(),

                Section::make('Usage Information')
                    ->schema([
                        TextEntry::make('agents_count')
                            ->label('Number of Agents Using This Prompt')
                            ->state(function ($record) {
                                return $record->agents()->count();
                            }),
                        TextEntry::make('chat_messages_count')
                            ->label('Number of Messages Using This Prompt')
                            ->state(function ($record) {
                                return $record->chatMessages()->count();
                            }),
                        TextEntry::make('created_at')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->dateTime(),
                    ])
                    ->columns(2),
            ]);
    }
}
