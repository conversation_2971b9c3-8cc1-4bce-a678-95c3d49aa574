<?php

namespace App\Filament\Resources\ChoicePlaceResource\Pages;

use App\Filament\Resources\ChoicePlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateChoicePlace extends CreateRecord
{
    protected static string $resource = ChoicePlaceResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['is_choice'] = true;
        
        return $data;
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
