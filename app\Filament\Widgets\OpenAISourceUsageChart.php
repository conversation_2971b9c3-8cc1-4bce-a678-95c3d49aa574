<?php

namespace App\Filament\Widgets;

use App\Models\OpenAIApiUsage;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class OpenAISourceUsageChart extends ChartWidget
{
    protected static ?string $heading = 'OpenAI API Usage by Source';

    protected static ?string $pollingInterval = '60s';

    protected function getData(): array
    {
        $sourceData = OpenAIApiUsage::query()
            ->select('source', DB::raw('SUM(total_tokens) as total_tokens'), DB::raw('SUM(total_cost) as total_cost'))
            ->groupBy('source')
            ->orderByDesc('total_tokens')
            ->get();

        // Store cost data for tooltips
        $costData = $sourceData->pluck('total_cost', 'source')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Token Usage by Source',
                    'data' => $sourceData->pluck('total_tokens')->toArray(),
                    'backgroundColor' => [
                        '#10B981',
                        '#F59E0B',
                        '#EF4444',
                        '#6366F1',
                        '#9061F9',
                        '#0EA5E9',
                    ],
                    'costData' => $costData, // Add cost data for tooltips
                ],
            ],
            'labels' => $sourceData->pluck('source')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => "function(context) {
                            const source = context.label;
                            const tokens = context.parsed.toLocaleString();
                            const cost = context.dataset.costData[source];
                            return [
                                source + ': ' + tokens + ' tokens',
                                'Cost: $' + parseFloat(cost).toFixed(6)
                            ];
                        }",
                    ],
                ],
            ],
        ];
    }
}
