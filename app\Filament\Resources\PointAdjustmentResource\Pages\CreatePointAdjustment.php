<?php

namespace App\Filament\Resources\PointAdjustmentResource\Pages;

use App\Filament\Resources\PointAdjustmentResource;
use Filament\Resources\Pages\CreateRecord;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class CreatePointAdjustment extends CreateRecord
{
    protected static string $resource = PointAdjustmentResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['admin_id'] = Auth::id();
        $user = User::find($data['user_id']);

        switch ($data['type']) {
            case 'add':
                $user->points += $data['amount'];
                break;
            case 'deduct':
                $user->points = max(0, $user->points - $data['amount']);
                break;
            case 'reset':
                $user->points = $data['amount'];
                break;
        }

        $user->save();

        return $data;
    }
}
