<?php

namespace App\Actions\Recommendations;

use App\Models\SystemSetting;
use App\Models\User;
use App\Models\UserRecommendationUsage;
use Illuminate\Support\Facades\Log;

class CheckRecommendationLimitAction
{
    /**
     * Check if a user has reached their recommendation limit
     *
     * @param User $user The user to check
     * @return array The result containing limit status and information
     */
    public function execute(User $user): array
    {
        try {
            // If user is anonymous, they don't have limits
            if ($user->is_anonymous) {
                return [
                    'limited' => false,
                    'remaining' => null,
                    'limit' => null,
                    'next_reset' => null,
                ];
            }

            // Check if the user has reached their limit
            $hasReachedLimit = $user->hasReachedRecommendationLimit();
            
            // Get the user's recommendation usage record
            $usage = UserRecommendationUsage::where('user_id', $user->id)->first();
            
            // Get the limit from system settings
            $limit = SystemSetting::getValue(
                SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED, 
                10
            );
            
            // Calculate remaining recommendations
            $remaining = $usage ? max(0, $limit - $usage->count) : $limit;
            
            // Get the next reset date
            $nextReset = $usage ? $usage->next_reset_at : null;
            
            return [
                'limited' => $hasReachedLimit,
                'remaining' => $remaining,
                'limit' => $limit,
                'next_reset' => $nextReset,
            ];
        } catch (\Exception $e) {
            Log::error('Error checking recommendation limit', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id
            ]);
            
            // Default to not limited in case of error
            return [
                'limited' => false,
                'remaining' => null,
                'limit' => null,
                'next_reset' => null,
            ];
        }
    }
}
