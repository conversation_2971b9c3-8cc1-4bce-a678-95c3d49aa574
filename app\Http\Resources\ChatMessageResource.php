<?php

namespace App\Http\Resources;

use App\Models\Place;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChatMessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'content' => $this->message_text,
            'type' => $this->is_recommended ? 'recommendation' : 'text',
            'sender' => $this->sender,
            'timestamp' => $this->created_at,
            'metadata' => [
                'completed' => true,
                'chat_session_id' => $this->chat_session_id,
                'agent_id' => $this->agent_id,
                'is_transition' => $this->is_transition,
                'is_recommended' => $this->is_recommended,
                'transition' => [
                    'from_agent_id' => $this->from_agent_id,
                    'to_agent_id' => $this->to_agent_id,
                    'prompt_starters' => $this->isLastMessageInConversation() ? $this->quick_replies : [],
                ]
            ]
        ];

        // Load recommendation places if this message has recommendations
        if ($this->is_recommended) {
            // Get the latest recommendation for this message
            $recommendation = $this->recommendations()->latest()->first();

            if ($recommendation && !empty($recommendation->places_ids)) {
                // Get places from the recommendation with eager loading
                $places = Place::with(['agent', 'media', 'category'])
                    ->whereIn('id', $recommendation->places_ids)
                    ->get();

                // Add recommendation_id to metadata
                $data['metadata']['recommendation_id'] = $recommendation->id;

                // Add one image for each place to metadata
                $placeImages = [];
                foreach ($places as $place) {
                    $media = $place->getMedia('place_photos');
                    if ($media->isNotEmpty()) {
                        $placeImages[] = $media->first()->getFullUrl();
                    }
                }

                if (!empty($placeImages)) {
                    $data['metadata']['images_places'] = $placeImages;
                }
            }
        }

        return $data;
    }

    /**
     * Check if this message is the last one in its conversation
     *
     * @return bool
     */
    protected function isLastMessageInConversation(): bool
    {
        // Load the chat session if not already loaded
        if (!$this->chatSession) {
            $this->load('chatSession');
        }

        // If the message's created_at matches the chat session's last_message_at, it's the last message
        // We also check if quick_replies exists since not all messages have quick replies
        return $this->quick_replies &&
               $this->chatSession &&
               $this->created_at->equalTo($this->chatSession->last_message_at);
    }
}
