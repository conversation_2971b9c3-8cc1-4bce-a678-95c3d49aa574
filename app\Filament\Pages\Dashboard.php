<?php

namespace App\Filament\Pages;

use App\Jobs\RenewGuestPointsJob;
use App\Jobs\RenewRegisteredPointsJob;
use App\Models\User;
use App\Services\TimeManipulationService;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Pages\Dashboard as BaseDashboard;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected function getHeaderActions(): array
    {
        return [
            Action::make('convertToRegistered')
                ->label('Convert to Registered User')
                ->icon('heroicon-o-user')
                ->color('success')
                ->visible(fn() => Auth::check() && Auth::user()->is_anonymous)
                ->form([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('email')
                        ->email()
                        ->required()
                        ->maxLength(255)
                        ->unique(User::class, 'email', ignoreRecord: true),
                    Forms\Components\TextInput::make('password')
                        ->password()
                        ->required()
                        ->maxLength(255),
                ])
                ->action(function (array $data) {
                    $user = Auth::user();

                    $user->update([
                        'name' => $data['name'],
                        'email' => $data['email'],
                        'password' => Hash::make($data['password']),
                        'is_anonymous' => false,
                        'email_verified_at' => null,
                    ]);

                    Auth::login($user);

                    Notification::make()
                        ->success()
                        ->title('Account Converted')
                        ->body('Your account has been successfully converted to a registered user account.')
                        ->send();
                }),

            // Subscribe to Rydo+ action
            Action::make('subscribeToRydoPlus')
                ->label('Subscribe to Rydo+')
                ->icon('heroicon-o-star')
                ->color('primary')
                ->visible(function () {
                    $user = Auth::user();
                    return Auth::check() &&
                        !$user->is_anonymous &&
                        $user->user_type === 'user' &&
                        !$user->hasActiveSubscription();
                })
                ->form(function () {
                    $rydoPlusPlan = \App\Models\Plan::where('code', \App\Enums\SubscriptionPlan::RYDO_PLUS->value)->first();

                    return [
                        Forms\Components\Section::make('Rydo+ Subscription')
                            ->description('Upgrade to Rydo+ for premium features and benefits')
                            ->schema([
                                Forms\Components\Placeholder::make('price')
                                    ->label('Price')
                                    ->content(function () use ($rydoPlusPlan) {
                                        return "{$rydoPlusPlan->price} {$rydoPlusPlan->currency} / {$rydoPlusPlan->duration_days} days";
                                    }),

                                Forms\Components\Placeholder::make('points')
                                    ->label('Points Included')
                                    ->content(function () use ($rydoPlusPlan) {
                                        return "{$rydoPlusPlan->points} points";
                                    }),

                                Forms\Components\Checkbox::make('terms')
                                    ->label('I agree to the terms and conditions')
                                    ->required()
                                    ->helperText('By subscribing, you agree to our terms of service and privacy policy.'),
                            ])
                    ];
                })
                ->action(function (array $data) {
                    $user = Auth::user();
                    $rydoPlusPlan = \App\Models\Plan::where('code', \App\Enums\SubscriptionPlan::RYDO_PLUS->value)->first();

                    // Create a new subscription
                    \App\Models\Subscription::create([
                        'user_id' => $user->id,
                        'plan' => \App\Enums\SubscriptionPlan::RYDO_PLUS->value,
                        'points' => $rydoPlusPlan->points,
                        'status' => \App\Enums\SubscriptionStatus::ACTIVE->value,
                        'start_date' => now(),
                        'end_date' => now()->addDays($rydoPlusPlan->duration_days),
                    ]);

                    // Add the subscription points to the user
                    $user->addSomePoints($rydoPlusPlan->points, 'Rydo+ subscription bonus points');

                    Notification::make()
                        ->success()
                        ->title('Subscription Created')
                        ->body("You have successfully subscribed to Rydo+! {$rydoPlusPlan->points} points have been added to your account.")
                        ->send();
                }),

            // Time Manipulation Actions (only visible to admins)
            ActionGroup::make([
                Action::make('editServerTime')
                    ->label('Edit Server Time')
                    ->icon('heroicon-o-calendar')
                    ->color('warning')
                    ->form([
                        Forms\Components\Section::make('Current Time Information')
                            ->schema([
                                Forms\Components\Placeholder::make('current_time')
                                    ->label('Current Server Time')
                                    ->content(fn() => Carbon::now()->format('Y-m-d H:i:s')),

                                Forms\Components\Placeholder::make('current_offset')
                                    ->label('Current Time Offset')
                                    ->content(fn() => TimeManipulationService::getTimeOffsetFormatted()),

                                Forms\Components\Placeholder::make('real_time')
                                    ->label('Real Server Time (No Offset)')
                                    ->content(fn() => Carbon::now()->setTimezone(config('app.timezone'))->format('Y-m-d H:i:s')),
                            ])
                            ->columns(1),

                        Forms\Components\Section::make('Set New Time')
                            ->schema([
                                Forms\Components\DateTimePicker::make('new_datetime')
                                    ->label('New Date & Time')
                                    ->seconds(true)
                                    ->default(fn() => TimeManipulationService::now())
                                    ->timezone(config('app.timezone'))
                                    ->required()
                                    ->helperText('Select the date and time you want to set for testing'),
                            ])
                            ->columns(1),
                    ])
                    ->action(function (array $data) {
                        $selectedDateTime = Carbon::parse($data['new_datetime']);
                        $currentRealTime = Carbon::now()->setTimezone(config('app.timezone'));

                        // Calculate the difference in minutes between the selected time and the current real time
                        $diffInMinutes = $currentRealTime->diffInMinutes($selectedDateTime, false);

                        // Set the time offset
                        $newTime = TimeManipulationService::setTimeOffsetMinutes($diffInMinutes);

                        Notification::make()
                            ->warning()
                            ->title('Server Time Modified')
                            ->body('The server time has been modified for testing purposes. New time: ' . $newTime->format('Y-m-d H:i:s'))
                            ->send();
                    }),

                Action::make('resetServerTime')
                    ->label('Reset Server Time')
                    ->icon('heroicon-o-arrow-path')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->action(function () {
                        $realTime = TimeManipulationService::resetTimeOffset();

                        Notification::make()
                            ->success()
                            ->title('Server Time Reset')
                            ->body('The server time has been reset to the actual time: ' . $realTime->format('Y-m-d H:i:s'))
                            ->send();
                    }),
            ])
                ->label('Time Testing Tools')
                ->icon('heroicon-o-clock')
                ->visible(fn() => Auth::check()),

            // Points Renewal Actions (only visible to admins)
            ActionGroup::make([
                Action::make('renewGuestPoints')
                    ->label('Renew Guest Points')
                    ->icon('heroicon-o-sparkles')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalDescription('This will renew points for all guest users.')
                    ->action(function () {
                        // Dispatch the job immediately
                        // RenewGuestPointsJob::dispatch();
                        Artisan::call('points:renew-guest');

                        Notification::make()
                            ->success()
                            ->title('Guest Points Renewal Initiated')
                            ->body('The guest points renewal process has been started.')
                            ->send();
                    }),

                Action::make('renewRegisteredPoints')
                    ->label('Renew Registered User Points')
                    ->icon('heroicon-o-sparkles')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalDescription('This will renew points for all registered users.')
                    ->action(function () {
                        // Dispatch the job immediately

                        // RenewRegisteredPointsJob::dispatch();
                        Artisan::call('points:renew-registered');

                        Notification::make()
                            ->success()
                            ->title('Registered User Points Renewal Initiated')
                            ->body('The registered user points renewal process has been started.')
                            ->send();
                    }),
            ])
                ->label('Points Renewal Tools')
                ->icon('heroicon-o-currency-dollar')
                ->visible(fn() => Auth::check()),
        ];
    }

    public function getSubheading(): ?string
    {
        // Display the current time with any offset applied
        $now = TimeManipulationService::now();
        $offset = TimeManipulationService::getTimeOffset();

        if ($offset !== 0) {
            return 'Current System Time: ' . $now->format('Y-m-d H:i:s') . ' (' . TimeManipulationService::getTimeOffsetFormatted() . ')';
        }

        return parent::getSubheading();
    }
}
