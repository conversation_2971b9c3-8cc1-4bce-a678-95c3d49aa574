<?php

namespace App\Filament\Actions;

use App\Enums\UserType;
use Filament\Facades\Filament;

class IsUserAction
{
    public static function handle(): bool
    {
        $user = Filament::auth()->user();
        return $user && $user->user_type === UserType::User->value;
    }
    
    public static function getUserId(): ?int
    {
        $user = Filament::auth()->user();
        return $user ? $user->id : null;
    }
}
