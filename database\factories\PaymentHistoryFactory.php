<?php

namespace Database\Factories;

use App\Models\PaymentHistory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentHistory>
 */
class PaymentHistoryFactory extends Factory
{
    protected $model = PaymentHistory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'amount' => $this->faker->randomFloat(2, 5, 100),
            'currency' => 'USD',
            'payment_method' => $this->faker->randomElement(['credit_card', 'paypal', 'apple_pay']),
            'status' => $this->faker->randomElement(['completed', 'pending', 'failed']),
            'transaction_id' => $this->faker->uuid,
        ];
    }
}
