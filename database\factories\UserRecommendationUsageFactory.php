<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserRecommendationUsage>
 */
class UserRecommendationUsageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => \App\Models\User::factory(),
            'count' => $this->faker->numberBetween(0, 10),
            'last_reset_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'next_reset_at' => $this->faker->dateTimeBetween('now', '+30 days'),
        ];
    }
}
