<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatSession extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'last_message_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];


    protected static function boot()
    {
        parent::boot();

        static::creating(function ($chatSession) {
            if (!$chatSession->session_token) {
                do {
                    $token = bin2hex(random_bytes(32));
                } while (static::where('session_token', $token)->exists());

                $chatSession->session_token = $token;
            }

            // Set a default title for new chat sessions
            if (!$chatSession->title) {
                $chatSession->title = 'New Conversation';
            }
        });
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    public function messages(): HasMany
    {
        return $this->hasMany(ChatMessage::class);
    }

    public function apiUsages(): HasMany
    {
        return $this->hasMany(OpenAIApiUsage::class);
    }

    /**
     * Calculate the total OpenAI API cost for this chat session.
     *
     * @return float
     */
    public function getTotalApiCostAttribute(): float
    {
        return $this->apiUsages()->sum('total_cost');
    }

    /**
     * Calculate the total OpenAI API tokens for this chat session.
     *
     * @return int
     */
    public function getTotalApiTokensAttribute(): int
    {
        return $this->apiUsages()->sum('total_tokens');
    }
}
