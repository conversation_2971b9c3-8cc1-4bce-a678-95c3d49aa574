<?php

namespace App\Filament\Resources\DeviceInfoResource\Pages;

use App\Filament\Resources\DeviceInfoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewDeviceInfo extends ViewRecord
{
    protected static string $resource = DeviceInfoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('User Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('user.name')
                            ->label('User Name'),
                        Infolists\Components\TextEntry::make('user.email')
                            ->label('User Email'),
                        Infolists\Components\IconEntry::make('user.is_anonymous')
                            ->label('Anonymous User')
                            ->boolean(),
                    ]),

                Infolists\Components\Section::make('Device Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('device_id')
                            ->label('Device ID'),
                        Infolists\Components\TextEntry::make('device_type')
                            ->label('Device Type')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'android' => 'success',
                                'ios' => 'info',
                                'web' => 'warning',
                                'desktop' => 'danger',
                                default => 'gray',
                            }),
                        Infolists\Components\TextEntry::make('device_model')
                            ->label('Device Model'),
                        Infolists\Components\TextEntry::make('device_brand')
                            ->label('Device Brand'),
                        Infolists\Components\TextEntry::make('os_version')
                            ->label('OS Version'),
                    ])->columns(2),

                Infolists\Components\Section::make('App Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('app_version')
                            ->label('App Version'),
                        Infolists\Components\TextEntry::make('app_version_name')
                            ->label('App Version Name'),
                        Infolists\Components\TextEntry::make('language')
                            ->label('Language'),
                    ])->columns(3),

                Infolists\Components\Section::make('Location Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('ip_address')
                            ->label('IP Address'),
                        Infolists\Components\TextEntry::make('location')
                            ->label('Location'),
                    ])->columns(2),

                Infolists\Components\Section::make('Timestamps')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                    ])->columns(2),
            ]);
    }
}
