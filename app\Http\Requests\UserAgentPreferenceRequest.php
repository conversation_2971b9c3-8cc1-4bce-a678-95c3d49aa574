<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserAgentPreferenceRequest extends FormRequest
{
   
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'agent_id' => 'required|exists:agents,id',
            'interest_level' => 'required|integer|min:1|max:5',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'agent_id.required' => 'The agent ID is required.',
            'agent_id.exists' => 'The selected agent does not exist.',
            'interest_level.required' => 'The interest level is required.',
            'interest_level.integer' => 'The interest level must be an integer.',
            'interest_level.min' => 'The interest level must be at least 1.',
            'interest_level.max' => 'The interest level must not be greater than 5.',
        ];
    }
}
