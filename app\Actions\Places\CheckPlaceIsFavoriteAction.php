<?php

namespace App\Actions\Places;

use App\Models\Place;
use App\Models\PlaceFavorite;
use App\Models\User;

class CheckPlaceIsFavoriteAction
{
    /**
     * Check if a place is favorited by a user
     *
     * @param User $user
     * @param Place $place
     * @return bool
     */
    public function execute(User $user, Place $place): bool
    {
        return PlaceFavorite::where('user_id', $user->id)
            ->where('place_id', $place->id)
            ->exists();
    }
}
