<?php

namespace App\Filament\Resources\AgentPromptStarterResource\Pages;

use App\Filament\Resources\AgentPromptStarterResource;
use App\Models\Agent;
use Filament\Actions;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Artisan;

class ListAgentPromptStarters extends ListRecords
{
    protected static string $resource = AgentPromptStarterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('generatePromptStarters')
                ->label('Generate Prompt Starters')
                ->icon('heroicon-o-sparkles')
                ->color('success')
                ->form([
                    Forms\Components\Select::make('agent_id')
                        ->label('Agent')
                        ->options(Agent::where('is_visible', true)
                            ->where('is_general', false)
                            ->pluck('name', 'id'))
                        ->placeholder('All Agents')
                        ->helperText('Leave empty to generate for all agents')
                        ->searchable(),
                ])
                ->action(function (array $data): void {
                    $agentId = $data['agent_id'] ?? null;

                    if ($agentId) {
                        // Generate for specific agent
                        Artisan::call('app:generate-agent-prompt-starters', [
                            '--agent' => $agentId,
                        ]);

                        $agent = Agent::find($agentId);
                        Notification::make()
                            ->title('Generating Prompt Starters')
                            ->body("Prompt starters are being generated for {$agent->name}. This may take a moment.")
                            ->success()
                            ->send();
                    } else {
                        // Generate for all agents
                        Artisan::call('app:generate-agent-prompt-starters');

                        Notification::make()
                            ->title('Generating Prompt Starters')
                            ->body('Prompt starters are being generated for all agents. This may take a moment.')
                            ->success()
                            ->send();
                    }
                }),
        ];
    }
}
