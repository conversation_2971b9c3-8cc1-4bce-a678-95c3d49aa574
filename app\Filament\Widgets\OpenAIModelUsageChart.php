<?php

namespace App\Filament\Widgets;

use App\Models\OpenAIApiUsage;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class OpenAIModelUsageChart extends ChartWidget
{
    protected static ?string $heading = 'OpenAI Model Usage Distribution';

    protected static ?string $pollingInterval = '60s';

    protected function getData(): array
    {
        $modelData = OpenAIApiUsage::query()
            ->select('model', DB::raw('SUM(total_tokens) as total_tokens'), DB::raw('SUM(total_cost) as total_cost'))
            ->groupBy('model')
            ->orderByDesc('total_tokens')
            ->get();

        // Store cost data for tooltips
        $costData = $modelData->pluck('total_cost', 'model')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Token Usage by Model',
                    'data' => $modelData->pluck('total_tokens')->toArray(),
                    'backgroundColor' => [
                        '#9061F9',
                        '#0EA5E9',
                        '#10B981',
                        '#F59E0B',
                        '#EF4444',
                        '#6366F1',
                    ],
                    'costData' => $costData, // Add cost data for tooltips
                ],
            ],
            'labels' => $modelData->pluck('model')->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => "function(context) {
                            const model = context.label;
                            const tokens = context.parsed.toLocaleString();
                            const cost = context.dataset.costData[model];
                            return [
                                model + ': ' + tokens + ' tokens',
                                'Cost: $' + parseFloat(cost).toFixed(6)
                            ];
                        }",
                    ],
                ],
            ],
        ];
    }
}
