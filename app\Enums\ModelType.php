<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum ModelType: string implements HasColor, HasLabel
{
    case GPT_4 = "gpt-4";
    case GPT_4o = "gpt-4o";
    case GPT_4o_MINI = "gpt-4o-mini";

    public function getLabel(): ?string
    {
        return match ($this) {
            self::GPT_4 => 'GPT-4',
            self::GPT_4o => 'GPT-4o',
            self::GPT_4o_MINI => 'GPT-4o Mini',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::GPT_4 => 'primary',
            self::GPT_4o => 'success',
            self::GPT_4o_MINI => 'info',
        };
    }
}
