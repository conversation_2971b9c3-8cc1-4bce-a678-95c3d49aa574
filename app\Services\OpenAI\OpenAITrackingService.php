<?php

namespace App\Services\OpenAI;

use App\Enums\ModelType;
use App\Models\OpenAIApiUsage;
use Illuminate\Support\Facades\Log;

class OpenAITrackingService
{
    // Token cost rates per 1M tokens (in USD)
    private const MODEL_RATES = [
        // GPT-4 models
        'gpt-4' => [
            'input' => 30.00,
            'output' => 60.00,
        ],
        'gpt-4o' => [
            'input' => 5.00,
            'output' => 15.00,
        ],
        'gpt-4o-mini' => [
            'input' => 0.60,
            'output' => 1.80,
        ],
        // Default fallback rates (using GPT-4 as default)
        'default' => [
            'input' => 30.00,
            'output' => 60.00,
        ],
    ];

    /**
     * Track OpenAI API usage
     *
     * @param string|ModelType $model The model used
     * @param int|null $promptTokens Number of prompt tokens
     * @param int|null $completionTokens Number of completion tokens
     * @param array $metadata Additional metadata
     * @param int|null $userId User ID if available
     * @param int|null $chatMessageId Chat message ID if available
     * @param string $source Source of the API call
     * @param string|null $action Specific action being performed
     * @param int|null $chatSessionId Chat session ID if available
     * @return OpenAIApiUsage
     */
    public function trackUsage(
        string|ModelType $model,
        ?int $promptTokens,
        ?int $completionTokens,
        array $metadata = [],
        ?int $userId = null,
        ?int $chatMessageId = null,
        string $source = 'unknown',
        ?string $action = null,
        ?int $chatSessionId = null
    ): OpenAIApiUsage {
        // Convert ModelType enum to string if needed
        $modelString = $model instanceof ModelType ? $model->value : $model;
        try {
            Log::info('Attempting to track OpenAI API usage', [
                'model' => $modelString,
                'has_chat_message' => $chatMessageId ? 'yes' : 'no',
                'chat_message_id' => $chatMessageId,
                'source' => $source,
                'metadata' => $metadata,
            ]);

            // Calculate costs
            $costs = $this->calculateCosts($modelString, $promptTokens, $completionTokens);

            // Prepare request type and endpoint from metadata if available
            $requestType = $metadata['request_type'] ?? null;
            $endpoint = $metadata['endpoint'] ?? null;

            // Create usage record
            Log::info('Creating OpenAI API usage record', [
                'user_id' => $userId,
                'chat_message_id' => $chatMessageId,
                'model' => $modelString,
                'source' => $source,
                'action' => $action,
            ]);

            $usage = OpenAIApiUsage::create([
                'user_id' => $userId,
                'chat_message_id' => $chatMessageId,
                'chat_session_id' => $chatSessionId,
                'model' => $modelString,
                'source' => $source,
                'action' => $action,
                'request_type' => $requestType,
                'endpoint' => $endpoint,
                'prompt_tokens' => $promptTokens ?? 0,
                'completion_tokens' => $completionTokens ?? 0,
                'total_tokens' => ($promptTokens ?? 0) + ($completionTokens ?? 0),
                'prompt_cost' => $costs['prompt_cost'],
                'completion_cost' => $costs['completion_cost'],
                'total_cost' => $costs['total_cost'],
                'metadata' => $metadata,
            ]);

            Log::info('Successfully created OpenAI API usage record', [
                'api_usage_id' => $usage->id,
                'model' => $modelString,
                'total_tokens' => $usage->total_tokens,
                'total_cost' => $usage->total_cost,
            ]);

            return $usage;
        } catch (\Exception $e) {
            Log::error('Failed to track OpenAI API usage', [
                'error' => $e->getMessage(),
                'model' => $modelString,
                'source' => $source,
            ]);

            // Create a minimal record to at least track that an API call was made
            return OpenAIApiUsage::create([
                'user_id' => $userId,
                'chat_message_id' => $chatMessageId,
                'chat_session_id' => $chatSessionId,
                'model' => $modelString,
                'source' => $source,
                'action' => $action,
                'metadata' => ['error' => $e->getMessage()],
            ]);
        }
    }

    /**
     * Calculate costs based on model and token counts
     *
     * @param string|ModelType $model
     * @param int|null $promptTokens
     * @param int|null $completionTokens
     * @return array
     */
    private function calculateCosts(string|ModelType $model, ?int $promptTokens, ?int $completionTokens): array
    {
        // Convert ModelType enum to string if needed
        $modelString = $model instanceof ModelType ? $model->value : $model;

        // Get rates for the model, fallback to default if not found
        $rates = self::MODEL_RATES[$modelString] ?? self::MODEL_RATES['default'];

        // Calculate costs (convert from per 1M tokens to per token)
        $promptCost = ($promptTokens ?? 0) * ($rates['input'] / 1000000);
        $completionCost = ($completionTokens ?? 0) * ($rates['output'] / 1000000);
        $totalCost = $promptCost + $completionCost;

        Log::info('Calculated costs', [
            'prompt_cost' => $promptCost,
            'completion_cost' => $completionCost,
            'total_cost' => $totalCost,
        ]);

        return [
            'prompt_cost' => $promptCost,
            'completion_cost' => $completionCost,
            'total_cost' => $totalCost,
        ];
    }
}
