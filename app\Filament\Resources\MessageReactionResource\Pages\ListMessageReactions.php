<?php

namespace App\Filament\Resources\MessageReactionResource\Pages;

use App\Filament\Resources\MessageReactionResource;
use App\Filament\Widgets\MessageReactionStats;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Pages\Concerns\ExposesTableToWidgets;

class ListMessageReactions extends ListRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = MessageReactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            MessageReactionStats::class,
        ];
    }
}
