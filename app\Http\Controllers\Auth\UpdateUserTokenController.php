<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;

class UpdateUserTokenController extends Controller
{
    /**
     * Update the user's token and return the user data
     * 
     * This endpoint is used to refresh the user's token and get the latest user data
     * It's called after login or when the app needs to refresh the user's data
     *
     * @param Request $request
     * @return UserResource
     */
    public function __invoke(Request $request)
    {
        $user = $request->user();
        
        // Mark that this is not the first login anymore
        if ($user->is_first_login) {
            $user->is_first_login = false;
            $user->save();
        }
        
        // Return the user with a fresh token
        return UserResource::make($user)
            ->withToken('user-token');
    }
}
