<?php

namespace App\Filament\Resources\MessageReportResource\Pages;

use App\Filament\Resources\MessageReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMessageReports extends ListRecords
{
    protected static string $resource = MessageReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
