<?php

namespace App\Filament\Widgets;

use App\Models\OpenAIApiUsage;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class OpenAIApiUsageOverview extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';

    protected function getStats(): array
    {
        // Get total tokens and cost for today
        $todayStats = OpenAIApiUsage::whereDate('created_at', today())
            ->selectRaw('SUM(total_tokens) as total_tokens, SUM(total_cost) as total_cost')
            ->first();

        // Get total tokens and cost for this month
        $monthStats = OpenAIApiUsage::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->selectRaw('SUM(total_tokens) as total_tokens, SUM(total_cost) as total_cost')
            ->first();

        // Get total tokens and cost for all time
        $allTimeStats = OpenAIApiUsage::selectRaw('SUM(total_tokens) as total_tokens, SUM(total_cost) as total_cost')
            ->first();

        return [
            Stat::make('Today\'s API Usage', number_format($todayStats->total_tokens ?? 0))
                ->description('Total tokens used today')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('Today\'s API Cost', '$' . number_format(($todayStats->total_cost ?? 0), 6))
                ->description('Total cost today')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('This Month\'s API Usage', number_format($monthStats->total_tokens ?? 0))
                ->description('Total tokens used this month')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('This Month\'s API Cost', '$' . number_format(($monthStats->total_cost ?? 0), 6))
                ->description('Total cost this month')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('All-Time API Usage', number_format($allTimeStats->total_tokens ?? 0))
                ->description('Total tokens used all time')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('All-Time API Cost', '$' . number_format(($allTimeStats->total_cost ?? 0), 6))
                ->description('Total cost all time')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),
        ];
    }
}
