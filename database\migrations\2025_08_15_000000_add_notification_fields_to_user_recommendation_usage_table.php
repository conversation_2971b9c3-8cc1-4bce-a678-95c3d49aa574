<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_recommendation_usage', function (Blueprint $table) {
            $table->boolean('has_been_notified_of_renewal')->default(false)->after('next_reset_at');
            $table->timestamp('last_notification_shown_at')->nullable()->after('has_been_notified_of_renewal');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_recommendation_usage', function (Blueprint $table) {
            $table->dropColumn(['has_been_notified_of_renewal', 'last_notification_shown_at']);
        });
    }
};
