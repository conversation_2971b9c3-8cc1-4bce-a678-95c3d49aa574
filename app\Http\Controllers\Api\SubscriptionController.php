<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProcessPurchaseRequest;
use App\Http\Requests\ReactivateSubscriptionRequest;
use App\Http\Resources\SubscriptionPlanResource;
use App\Http\Resources\SubscriptionStatusResource;
use App\Models\Subscription;
use App\Services\PurchaseValidationService;
use App\Services\SubscriptionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\SubscriptionResource;

class SubscriptionController extends Controller
{
    public function __construct(
        private SubscriptionService $subscriptionService,
        private PurchaseValidationService $purchaseValidationService
    ) {}

    /**
     * Get available subscription plans
     *
     * @param Request $request
     * @return SubscriptionPlanResource|JsonResponse
     */
    public function getPlans(Request $request): SubscriptionPlanResource|JsonResponse
    {
        try {
            $plan = $this->subscriptionService->getAvailablePlans();

            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'No subscription plans available'
                ], 404);
            }

            $user = $request->user();
            $userStatus = $this->subscriptionService->getUserSubscriptionStatus($user);

            return SubscriptionPlanResource::make($plan)
                    ->additional([
                        'user_status' => [
                            'has_active_subscription' => $userStatus['has_active_subscription'],
                            'current_plan' => $userStatus['current_subscription']?->plan?->value,
                        ]
                   ]);

        } catch (\Exception $e) {
            Log::error('Error fetching subscription plans', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch subscription plans'
            ], 500);
        }
    }

    /**
     * Get current subscription status
     *
     * @param Request $request
     * @return SubscriptionStatusResource
     */
    public function getStatus(Request $request): SubscriptionStatusResource
    {
        try {
            $user = $request->user();

            return SubscriptionStatusResource::make($user);
        } catch (\Exception $e) {
            Log::error('Error fetching subscription status', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch subscription status'
            ], 500);
        }
    }

    /**
     * Process purchase
     *
     * @param ProcessPurchaseRequest $request
     * @return SubscriptionResource|JsonResponse
     */
    public function processPurchase(ProcessPurchaseRequest $request): SubscriptionResource|JsonResponse
    {
        try {
            $user = $request->user();
            $platform = $request->input('platform');
            $productId = $request->input('product_id');
            $receiptData = $request->input('receipt_data');
            $transactionId = $request->input('transaction_id');
            $isReactivation = $request->boolean('is_reactivation', false);

            // Check for duplicate transaction
            if ($this->purchaseValidationService->isDuplicateTransaction($transactionId)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'DUPLICATE_TRANSACTION',
                        'message' => 'This transaction has already been processed'
                    ]
                ], 409);
            }

            // Validate receipt
            if (!$this->purchaseValidationService->validateReceipt($platform, $receiptData, $transactionId)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_RECEIPT',
                        'message' => 'Receipt validation failed'
                    ]
                ], 400);
            }

            // Process the purchase
            $subscription = $this->subscriptionService->processPurchase(
                $user,
                $platform,
                $productId,
                $transactionId,
                $isReactivation,
                $receiptData
            );

            return SubscriptionResource::make($subscription);

        } catch (\Exception $e) {
            Log::error('Error processing purchase', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'platform' => $request->input('platform'),
                'transaction_id' => $request->input('transaction_id')
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'PURCHASE_PROCESSING_FAILED',
                    'message' => 'Failed to process purchase. Please try again.'
                ]
            ], 500);
        }
    }

    /**
     * Check subscription eligibility
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkEligibility(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $eligibility = $this->subscriptionService->checkEligibility($user);

            return response()->json([
                'success' => true,
                'data' => $eligibility
            ]);

        } catch (\Exception $e) {
            Log::error('Error checking subscription eligibility', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check subscription eligibility'
            ], 500);
        }
    }

    /**
     * Check reactivation eligibility
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkReactivationEligibility(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $eligibility = $this->subscriptionService->getReactivationEligibility($user);

            return response()->json([
                'success' => true,
                'data' => $eligibility
            ]);

        } catch (\Exception $e) {
            Log::error('Error checking reactivation eligibility', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check reactivation eligibility'
            ], 500);
        }
    }

    /**
     * Reactivate user's subscription
     *
     * @param ReactivateSubscriptionRequest $request
     * @return SubscriptionResource|JsonResponse
     */
    public function reactivateSubscription(ReactivateSubscriptionRequest $request): SubscriptionResource|JsonResponse
    {
        try {
            $user = $request->user();
            $platform = $request->input('platform');
            $productId = $request->input('product_id');
            $receiptData = $request->input('receipt_data');
            $transactionId = $request->input('transaction_id');

            // Check if user can reactivate subscription
            if (!$this->subscriptionService->canUserReactivate($user)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'REACTIVATION_NOT_ELIGIBLE',
                        'message' => 'User is not eligible for subscription reactivation'
                    ]
                ], 400);
            }

            // Check for duplicate transaction
            if ($this->purchaseValidationService->isDuplicateTransaction($transactionId)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'DUPLICATE_TRANSACTION',
                        'message' => 'This transaction has already been processed'
                    ]
                ], 409);
            }

            // Validate receipt
            if (!$this->purchaseValidationService->validateReceipt($platform, $receiptData, $transactionId)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_RECEIPT',
                        'message' => 'Receipt validation failed'
                    ]
                ], 400);
            }

            // Reactivate the subscription
            $subscription = $this->subscriptionService->reactivateSubscription(
                $user,
                $platform,
                $productId,
                $transactionId,
                $receiptData
            );

            return SubscriptionResource::make($subscription);

        } catch (\Exception $e) {
            Log::error('Error reactivating subscription', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'platform' => $request->input('platform'),
                'transaction_id' => $request->input('transaction_id'),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'REACTIVATION_FAILED',
                    'message' => 'Failed to reactivate subscription. Please try again.'
                ]
            ], 500);
        }
    }

    /**
     * Get user's most recent subscription (active or expired)
     *
     * @param Request $request
     * @return SubscriptionResource
     */
    public function getLastSubscription(Request $request): SubscriptionResource
    {
        $user = $request->user();

        // Get the most recent subscription with plan details
        $lastSubscription = Subscription::where('user_id', $user->id)
            ->with('planDetails')
            ->latest('created_at')
            ->first();

        return SubscriptionResource::make($lastSubscription);
    }

    /**
     * Get paginated subscription history for the authenticated user
     *
     * @param Request $request
     * @return ResourceCollection|JsonResponse
     */
    public function getSubscriptionHistory(Request $request)
    {

            $user = $request->user();
            $perPage = $request->input('per_page', 15);

            // Get paginated subscriptions with plan details, ordered by creation date (newest first)
            $subscriptions = Subscription::where('user_id', $user->id)
                ->with('planDetails')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return SubscriptionResource::collection($subscriptions);
    }

    /**
     * Cancel user's active subscription
     *
     * @param Request $request
     * @return SubscriptionResource
     */
    public function cancelSubscription(Request $request): SubscriptionResource
    {
        try {
            $user = $request->user();

            // Check if user can cancel subscription
            if (!$this->subscriptionService->canUserCancelSubscription($user)) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'NO_ACTIVE_SUBSCRIPTION',
                        'message' => 'No active subscription found to cancel'
                    ]
                ], 400);
            }

            // Cancel the subscription
            $subscription = $this->subscriptionService->cancelSubscription($user);

            return SubscriptionResource::make($subscription);

        } catch (\Exception $e) {
            Log::error('Error cancelling subscription', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'CANCELLATION_FAILED',
                    'message' => 'Failed to cancel subscription. Please try again.'
                ]
            ], 500);
        }
    }
}
