<?php

namespace App\Filament\Resources\PromptResource\Pages;

use App\Filament\Resources\PromptResource;
use App\Models\Agent;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditPrompt extends EditRecord
{
    protected static string $resource = PromptResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['agent_id'] = $this->record->agents->pluck('id')->toArray();

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Update the prompt record with the form data
        $record->update($data);

        // Get the selected agent IDs from the form
        $agentIds = $this->data['agent_id'] ?? [];

        // Get the current agents using this prompt
        $currentAgentIds = $record->agents->pluck('id')->toArray();

        // Find agents that were removed (in current but not in selected)
        $removedAgentIds = array_diff($currentAgentIds, $agentIds);

        // Find agents that were added (in selected but not in current)
        $addedAgentIds = array_diff($agentIds, $currentAgentIds);

        // Update agents that were added to use this prompt
        if (!empty($addedAgentIds)) {
            Agent::whereIn('id', $addedAgentIds)
                ->update(['prompt_id' => $record->id]);
        }

        // For removed agents, we need to handle this carefully
        // In a real application, you might want to set them to a default prompt
        // or require that agents always have a prompt
        if (!empty($removedAgentIds)) {
            // Here we're just logging that agents were removed
            // You might want to implement a different behavior
            // For example, you could set them to use a default prompt
            // Agent::whereIn('id', $removedAgentIds)->update(['prompt_id' => $defaultPromptId]);
        }

        return $record;
    }
}
