<?php

namespace App\Http\Controllers\Auth;

use App\Events\UserDeleted;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DeleteAccountController extends Controller
{
    /**
     * Handle the account deletion request.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Generate a unique email for the deleted user
        $newEmail = $user->email . '_' . Str::random(10) . '_' . time() . '@deleted.com';
        
        DB::transaction(function () use ($user, $newEmail) {
            // Update user's email to prevent conflicts with future registrations
            $user->email = $newEmail;
            $user->save();
            
            // Dispatch event to handle related data deletion
            event(new UserDeleted($user, $newEmail, $user->id));
            
            // Delete all tokens to revoke access
            $user->tokens()->delete();

            // Soft delete the user
            $user->delete();
        });
        
        return response()->json([
            'message' => 'Account deleted successfully'
        ]);
    }
}
