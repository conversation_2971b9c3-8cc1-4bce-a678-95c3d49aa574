<?php

namespace App\Actions\Chat;

use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Enums\ChatMessageSender;

class CreateUserMessageAction
{
    /**
     * Create a user message in the chat session
     */
    public function execute(ChatSession $chatSession, $user, Agent $agent, string $messageText): ChatMessage
    {
        return ChatMessage::create([
            'chat_session_id' => $chatSession->id,
            'user_id' => $user->id,
            'agent_id' => $agent->id,
            'prompt_id' => $agent->prompt_id,
            'message_text' => $messageText,
            'sender' => ChatMessageSender::USER,
        ]);
    }
}
