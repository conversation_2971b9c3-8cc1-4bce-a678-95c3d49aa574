<?php

namespace App\Actions\Chat;

use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Enums\ChatMessageSender;

class BuildPromptContextAction
{
    /**
     * Build the prompt context for the AI service
     */
    public function execute(ChatSession $chatSession, ChatMessage $userMessage, array $analysisResult = []): string
    {
        // Get the agent's prompt
        $agent = $chatSession->agent;
        $promptTemplate = $agent->prompt->content ?? '';

        // Get previous messages for context (last 10 messages)
        $previousMessages = ChatMessage::query()
            ->where('chat_session_id', $chatSession->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->reverse();

        // Build the context
        $context = "{$promptTemplate}\n\n";

        // If we're going to transition, create a simple transitional response
        if (isset($analysisResult['should_transition']) && $analysisResult['should_transition']) {
            $targetAgent = $analysisResult['recommended_agent'];

            // Create a shortened message history for a more direct response
            $context .= "IMPORTANT INSTRUCTION: The user has asked about {$targetAgent->description}, which is outside your primary expertise. ";
            $context .= "Briefly acknowledge their request and let them know you're connecting them to {$targetAgent->name}, our specialist for {$targetAgent->description}. ";
            $context .= "Keep your response very short and focused only on the transition.\n\n";

            // Just include the most recent message for streamlined context
            $context .= "User: {$userMessage->message_text}\nAssistant:";

            return $context;
        }

        // For non-transition cases, include normal context
        $context .= $this->addAgentSwitchingInstructions($agent);

        foreach ($previousMessages as $message) {
            $role = $message->sender === ChatMessageSender::USER ? 'User' : 'Assistant';
            $context .= "{$role}: {$message->message_text}\n";
        }

        $context .= "User: {$userMessage->message_text}\nAssistant:";

        return $context;
    }

    /**
     * Add agent switching instructions to the prompt
     */
    protected function addAgentSwitchingInstructions(Agent $currentAgent): string
    {
        $instructions = "\nIf a question is outside your area of expertise, guide the user to the relevant agent:\n";

        // Get other visible agents
        $otherAgents = Agent::where('id', '!=', $currentAgent->id)
            ->where('is_visible', true)
            ->get();

        foreach ($otherAgents as $agent) {
            $instructions .= "- Suggest agent number {$agent->id} called {$agent->name} for {$agent->description}\n";
        }

        $instructions .= "\nIf the user asks to speak to one of the agents, only return ```switch : agent_id``` as a response.\n\n";

        return $instructions;
    }
}
