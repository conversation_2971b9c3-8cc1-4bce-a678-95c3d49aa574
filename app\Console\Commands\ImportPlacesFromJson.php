<?php

namespace App\Console\Commands;

use Database\Seeders\PlaceRecommendationSeeder;
use Illuminate\Console\Command;

class ImportPlacesFromJson extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-places-from-json';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import places from the JSON file to the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting import of places from JSON file...');
        
        $seeder = new PlaceRecommendationSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('Import completed!');
        
        return Command::SUCCESS;
    }
}
