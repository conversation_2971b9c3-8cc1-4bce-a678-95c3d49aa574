<?php

namespace App\Filament\Widgets;

use App\Models\ChatSession;
use App\Models\OpenAIApiUsage;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class ChatSessionApiCostChart extends ChartWidget
{
    protected static ?string $heading = 'Top Chat Sessions by API Cost';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $chatSessions = OpenAIApiUsage::query()
            ->select('chat_session_id', DB::raw('SUM(total_cost) as total_cost'))
            ->whereNotNull('chat_session_id')
            ->groupBy('chat_session_id')
            ->orderByDesc('total_cost')
            ->limit(10)
            ->get();

        // Get the chat session titles
        $sessionIds = $chatSessions->pluck('chat_session_id')->toArray();
        $sessionTitles = ChatSession::whereIn('id', $sessionIds)
            ->get()
            ->mapWithKeys(function ($session) {
                return [
                    $session->id => $session->title ?? ('Chat #' . $session->id)
                ];
            })
            ->toArray();

        // Prepare the data
        $labels = [];
        $costs = [];

        foreach ($chatSessions as $session) {
            $sessionId = $session->chat_session_id;
            $title = $sessionTitles[$sessionId] ?? ('Chat #' . $sessionId);
            
            // Truncate long titles
            if (strlen($title) > 20) {
                $title = substr($title, 0, 17) . '...';
            }
            
            $labels[] = $title;
            $costs[] = round($session->total_cost, 4);
        }

        return [
            'datasets' => [
                [
                    'label' => 'API Cost (USD)',
                    'data' => $costs,
                    'backgroundColor' => [
                        '#9061F9',
                        '#0EA5E9',
                        '#10B981',
                        '#F59E0B',
                        '#EF4444',
                        '#6366F1',
                        '#8B5CF6',
                        '#EC4899',
                        '#14B8A6',
                        '#F97316',
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
