<?php

namespace Tests\Feature\Api;

use App\Enums\UserType;
use App\Models\Place;
use App\Models\SystemSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NearbyPlacesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'user_type' => UserType::Admin,
        ]);

        // Create the system setting for nearby places distance
        SystemSetting::updateOrCreate(
            ['key' => SystemSetting::KEY_NEARBY_PLACES_DISTANCE],
            [
                'key' => SystemSetting::KEY_NEARBY_PLACES_DISTANCE,
                'value' => '5',
                'description' => 'Default distance in kilometers for nearby places search',
                'type' => 'integer',
                'is_public' => true,
            ]
        );
    }

    /** @test */
    public function it_returns_places_within_the_configured_distance()
    {
        self::markTestSkipped();
        // Create a user location (Dubai Mall coordinates)
        $userLat = 25.197197;
        $userLng = 55.274376;

        // Create places at different distances
        // Place 1: Within 5km (Burj Khalifa)
        $place1 = Place::factory()->create([
            'name' => 'Burj Khalifa',
            'latitude' => 25.197525,
            'longitude' => 55.274288,
            'is_active' => true,
        ]);

        // Place 2: Within 5km (Dubai Fountain)
        $place2 = Place::factory()->create([
            'name' =>  'Dubai Fountain',
            'latitude' => 25.194905,
            'longitude' => 55.276089,
            'is_active' => true,
        ]);

        // Place 3: Outside 5km (Palm Jumeirah)
        $place3 = Place::factory()->create([
            'name' => 'Palm Jumeirah',
            'latitude' => 25.112284,
            'longitude' => 55.138784,
            'is_active' => true,
        ]);

        // Make the request
        $response = $this->getJson("/api/map/places?lat={$userLat}&lng={$userLng}");

        // Assert response status
        $response->assertStatus(200);

        // Assert that only places within 5km are returned
        $response->assertJsonCount(2, 'data');
        $response->assertJsonFragment(['title' => 'Burj Khalifa']);
        $response->assertJsonFragment(['title' => 'Dubai Fountain']);
        $response->assertJsonMissing(['title' => 'Palm Jumeirah']);
    }

    /** @test */
    public function it_returns_empty_collection_when_no_places_are_within_distance()
    {
        self::markTestSkipped();

        // Create a user location in the middle of nowhere
        $userLat = 24.0;
        $userLng = 54.0;

        // Create a place far away
        Place::factory()->create([
            'name' => 'Far Away Place',
            'latitude' => 25.197525,
            'longitude' => 55.274288,
            'is_active' => true,
        ]);

        // Make the request
        $response = $this->getJson("/api/map/places?lat={$userLat}&lng={$userLng}");

        // Assert response status
        $response->assertStatus(200);

        // Assert that no places are returned
        $response->assertJsonCount(0, 'data');
    }

    /** @test */
    public function it_validates_latitude_and_longitude_parameters()
    {
        self::markTestSkipped();

        // Missing parameters
        $this->getJson('/api/map/places')
            ->assertStatus(422)
            ->assertJsonValidationErrors(['lat', 'lng']);

        // Invalid latitude (out of range)
        $this->getJson('/api/map/places?lat=100&lng=55.274376')
            ->assertStatus(422)
            ->assertJsonValidationErrors(['lat']);

        // Invalid longitude (out of range)
        $this->getJson('/api/map/places?lat=25.197197&lng=200')
            ->assertStatus(422)
            ->assertJsonValidationErrors(['lng']);

        // Non-numeric values
        $this->getJson('/api/map/places?lat=abc&lng=def')
            ->assertStatus(422)
            ->assertJsonValidationErrors(['lat', 'lng']);
    }

    /** @test */
    public function it_respects_the_system_setting_for_distance()
    {
        self::markTestSkipped();

        // Update the system setting to 1km
        SystemSetting::where('key', SystemSetting::KEY_NEARBY_PLACES_DISTANCE)
            ->update(['value' => '1']);

        // Create a user location (Dubai Mall coordinates)
        $userLat = 25.197197;
        $userLng = 55.274376;

        // Create places at different distances
        // Place 1: Within 1km (Burj Khalifa)
        $place1 = Place::factory()->create([
            'name' => 'Burj Khalifa',
            'latitude' => 25.197525,
            'longitude' => 55.274288,
            'is_active' => true,
        ]);

        // Place 2: Outside 1km but within 5km (DIFC)
        $place2 = Place::factory()->create([
            'name' => 'DIFC',
            'latitude' => 25.211760,
            'longitude' => 55.275147,
            'is_active' => true,
        ]);

        // Make the request
        $response = $this->getJson("/api/map/places?lat={$userLat}&lng={$userLng}");

        // Assert response status
        $response->assertStatus(200);

        // Assert that only places within 1km are returned
        $response->assertJsonCount(1, 'data');
        $response->assertJsonFragment(['title' => 'Burj Khalifa']);
        $response->assertJsonMissing(['title' => 'DIFC']);
    }
}
