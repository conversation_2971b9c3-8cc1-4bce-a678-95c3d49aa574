<?php

namespace App\Filament\Pages\Auth;

use App\Enums\UserType;
use App\Models\ReferralLog;
use App\Models\SystemSetting;
use App\Models\User;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Auth\Register as BaseRegister;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;

class Register extends BaseRegister
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getEmailFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getReferralCodeComponent(),
            ])
            ->statePath('data');
    }

    protected function getReferralCodeComponent(): Component
    {
        return TextInput::make('referral_code')
            ->label('Referral Code (Optional)')
            ->maxLength(255)
            ->helperText('Enter a referral code if you were referred by another user');
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label('Email')
            ->email()
            ->required()
            ->maxLength(255)
            ->unique(User::class)
            ->autocomplete('email');
    }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label('Password')
            ->password()
            ->required()
            ->minLength(8)
            ->autocomplete('new-password');
    }

    protected function handleRegistration(array $data): Model
    {
        $defaultPoints = SystemSetting::getValue(SystemSetting::KEY_DEFAULT_GUEST_POINTS, 100);
        $referralCode = $data['referral_code'] ?? null;
        $referrer = null;

        if ($referralCode) {
            $referrer = User::where('referral_code', $referralCode)->first();
        }

        $user = User::create([
            'name' => 'Guest-' . Str::random(8),
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'is_anonymous' => true,
            'user_type' => UserType::User,
            'email_verified_at' => null,
            'is_demo' => false,
            'referred_by' => $referrer?->id,
            'referral_code' => $this->generateUniqueReferralCode(),
        ]);

        // Process referral if applicable
        if ($referrer) {
            $this->processReferral($referrer, $user);

            Notification::make()
                ->title('Welcome!')
                ->body("Your guest account has been created with bonus points from referral!")
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Welcome!')
                ->body("Your guest account has been created with {$defaultPoints} bonus points!")
                ->success()
                ->send();
        }

        return $user;
    }

    protected function generateUniqueReferralCode(): string
    {
        $code = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

        // Ensure the code is unique
        while (User::where('referral_code', $code)->exists()) {
            $code = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
        }

        return $code;
    }

    protected function processReferral(User $referrer, User $newUser): void
    {
        // Check if referral system is active
        $isReferralActive = SystemSetting::getValue(SystemSetting::KEY_REFERRAL_SYSTEM_ACTIVE, false);

        if (!$isReferralActive) {
            return;
        }

        // Get points per referral from system settings
        $pointsPerReferral = SystemSetting::getValue(SystemSetting::KEY_REFERRAL_POINTS_PER_REFERRAL, 50);

        // Add points to referrer
        $referrer->addSomePoints(
            $pointsPerReferral,
            'Referral bonus for inviting ' . $newUser->name,
            null,
            'referral'
        );

        // Add points to new user
        $newUser->addSomePoints(
            $pointsPerReferral,
            'Welcome bonus from referral by ' . $referrer->name,
            null,
            'referral'
        );

        // Create referral log
        ReferralLog::create([
            'referrer_id' => $referrer->id,
            'referred_user_id' => $newUser->id,
            'points_awarded' => $pointsPerReferral,
            'status' => 'completed',
            'notes' => 'Successful referral registration'
        ]);
    }

    protected function getFormActions(): array
    {
        return [
            $this->getRegisterFormAction()
                ->label('Create Guest Account')
                ->extraAttributes(['class' => 'w-full']),
        ];
    }

    protected function hasFullWidthFormActions(): bool
    {
        return true;
    }
}
