<?php

namespace Database\Factories;

use App\Models\FeatureUsageStatistic;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FeatureUsageStatistic>
 */
class FeatureUsageStatisticFactory extends Factory
{
    protected $model = FeatureUsageStatistic::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'feature_type' => $this->faker->randomElement(['chat', 'explore']),
            'usage_count' => $this->faker->numberBetween(1, 100),
            'date' => $this->faker->dateTimeBetween('-1 month', 'now')->format('Y-m-d'),
        ];
    }
}
