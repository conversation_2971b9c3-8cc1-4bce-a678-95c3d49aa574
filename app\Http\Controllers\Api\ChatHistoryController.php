<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\ChatMessageResource;
use App\Http\Resources\ChatSessionResource;
use App\Models\ChatSession;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ChatHistoryController extends Controller
{

    /**
     * Get paginated list of chat sessions for the authenticated user
     *
     * @param Request $request
     * @return ResourceCollection
     */
    public function index(Request $request)
    {
        $userID = $request->user()->id;
        $perPage = $request->input('per_page', 15);

        // Get chat sessions with <PERSON><PERSON>'s built-in pagination
        $chatSessions = ChatSession::query()
            ->where('user_id', $userID)
            ->orderBy('last_message_at', 'desc')
            ->with('agent')
            ->paginate($perPage);

        return ChatSessionResource::collection($chatSessions);
    }

    public function show(Request $request, ChatSession $chatSession): ResourceCollection
    {
        $perPage = $request->input('per_page', 20);
        $page = $request->input('page', 1);

        // Load the chat messages with pagination
        $messages = $chatSession->messages()
            ->where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc') // Most recent first for pagination
            ->paginate($perPage, ['*'], 'page', $page);

        return ChatMessageResource::collection($messages);
    }

    /**
     * Delete a chat session
     *
     * @param ChatSession $chatSession
     * @return JsonResponse
     */
    public function destroy(ChatSession $chatSession): JsonResponse
    {
        // Delete the chat session (this will cascade delete messages due to foreign key constraints)
        $chatSession->delete();

        return response()->json([
            'success' => true,
            'chat_session_id' => $chatSession->id,
            'message' => 'Chat session deleted successfully'
        ]);
    }
}
