<?php

namespace App\Filament\Widgets;

use App\Models\Agent;
use App\Models\ChatSession;
use App\Models\OpenAIApiUsage;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class AgentApiCostChart extends ChartWidget
{
    protected static ?string $heading = 'Average API Cost per Chat Session by Agent';

    protected static ?int $sort = 4;

    protected function getData(): array
    {
        // Get the total cost and count of chat sessions for each agent
        $agentCosts = DB::table('openai_api_usages')
            ->join('chat_sessions', 'openai_api_usages.chat_session_id', '=', 'chat_sessions.id')
            ->join('agents', 'chat_sessions.agent_id', '=', 'agents.id')
            ->select(
                'agents.id as agent_id',
                'agents.name as agent_name',
                DB::raw('SUM(openai_api_usages.total_cost) as total_cost'),
                DB::raw('COUNT(DISTINCT openai_api_usages.chat_session_id) as session_count')
            )
            ->whereNotNull('openai_api_usages.chat_session_id')
            ->groupBy('agents.id', 'agents.name')
            ->orderByDesc('total_cost')
            ->get();

        // Calculate average cost per session
        $labels = [];
        $avgCosts = [];
        $colors = [
            '#9061F9',
            '#0EA5E9',
            '#10B981',
            '#F59E0B',
            '#EF4444',
            '#6366F1',
            '#8B5CF6',
            '#EC4899',
            '#14B8A6',
            '#F97316',
        ];

        foreach ($agentCosts as $index => $agent) {
            $avgCost = $agent->session_count > 0 
                ? $agent->total_cost / $agent->session_count 
                : 0;
            
            $labels[] = $agent->agent_name;
            $avgCosts[] = round($avgCost, 4);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Avg. Cost per Session (USD)',
                    'data' => $avgCosts,
                    'backgroundColor' => array_slice($colors, 0, count($avgCosts)),
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
