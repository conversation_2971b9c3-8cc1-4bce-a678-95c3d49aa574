<?php

namespace App\Filament\Resources\AdditionalInfoResource\Pages;

use App\Filament\Resources\AdditionalInfoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAdditionalInfos extends ListRecords
{
    protected static string $resource = AdditionalInfoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
