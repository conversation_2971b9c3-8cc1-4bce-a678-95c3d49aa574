<?php

namespace App\Filament\Resources\PointAdjustmentResource\Pages;

use App\Filament\Resources\PointAdjustmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

class ListPointAdjustments extends ListRecords
{
    protected static string $resource = PointAdjustmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('user.name')
                    ->label('User')
                    ->searchable()
                    ->sortable(),
                // TextColumn::make('admin.name')
                //     ->label('Admin')
                //     ->searchable()
                //     ->sortable(),
                BadgeColumn::make('type')
                    ->colors([
                        'success' => 'add',
                        'danger' => 'deduct',
                        'warning' => 'reset',
                    ]),
                TextColumn::make('amount')
                    ->label('Points')
                    ->sortable(),
                TextColumn::make('reason')
                    ->label('Reason')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('type')
                    ->options([
                        'add' => 'Add Points',
                        'deduct' => 'Deduct Points',
                        'reset' => 'Reset Points',
                    ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
