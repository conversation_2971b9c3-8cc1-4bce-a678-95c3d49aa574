<?php

namespace App\Filament\Resources\ChoicePlaceResource\Pages;

use App\Filament\Resources\ChoicePlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;

class ViewChoicePlace extends ViewRecord
{
    protected static string $resource = ChoicePlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('removeFromChoices')
                ->label('Remove from Choices')
                ->color('danger')
                ->icon('heroicon-o-x-mark')
                ->action(function () {
                    $this->record->update(['is_choice' => false]);
                    $this->redirect(ChoicePlaceResource::getUrl());
                }),
        ];
    }
    
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        TextEntry::make('name')
                            ->limit(50)
                            ->label('Name'),
                        TextEntry::make('brief_description') 
                            ->limit(50)
                            ->label('Brief Description'),
                        TextEntry::make('description')
                            ->limit(50)
                            ->markdown()
                            ->columnSpanFull(),
                        TextEntry::make('rating')
                            ->numeric(decimalPlaces: 1)
                            ->suffix(' / 5.0'),
                    ])
                    ->columns(2),

                Section::make('Location Details')
                    ->schema([
                        TextEntry::make('place_id')
                            ->label('Place ID'),
                        TextEntry::make('address.en')
                            ->label('Address'),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('latitude')
                                    ->numeric(decimalPlaces: 8),
                                TextEntry::make('longitude')
                                    ->numeric(decimalPlaces: 8),
                            ]),
                    ])
                    ->columns(2),

                Section::make('Media Gallery')
                    ->schema([
                        SpatieMediaLibraryImageEntry::make('place_photos')
                            ->label('Photos')
                            ->collection('place_photos')
                            ->columnSpanFull(),
                    ]),

                Section::make('Favorites Information')
                    ->schema([
                        TextEntry::make('favorites_count')
                            ->label('Total Favorites')
                            ->state(function ($record) {
                                return $record->favorites()->count();
                            }),
                        TextEntry::make('planned_favorites')
                            ->label('Planned Visits')
                            ->state(function ($record) {
                                return $record->favorites()->where('type', 'planned')->count();
                            }),
                        TextEntry::make('visited_favorites')
                            ->label('Visited')
                            ->state(function ($record) {
                                return $record->favorites()->where('type', 'visited')->count();
                            }),
                    ])
                    ->columns(3),
            ]);
    }
}
