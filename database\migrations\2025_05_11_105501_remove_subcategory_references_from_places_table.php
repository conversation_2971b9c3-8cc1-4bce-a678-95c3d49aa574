<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Remove subcategory_id foreign key and column from places table
     * Remove subcategories JSON column from places table
     */
    public function up(): void
    {
        Schema::table('places', function (Blueprint $table) {
            // Drop the foreign key constraint for subcategory_id
            $table->dropForeign(['subcategory_id']);

            // Drop the subcategory_id column
            $table->dropColumn('subcategory_id');

            // Drop the subcategories JSON column
            $table->dropColumn('subcategories');
        });
    }

    /**
     * Reverse the migrations.
     * This is a destructive migration, so the down method would need to recreate
     * the columns and constraints that were removed.
     */
    public function down(): void
    {
        Schema::table('places', function (Blueprint $table) {
            // Add back the subcategories JSON column
            $table->json('subcategories')->nullable();

            // Add back the subcategory_id column
            $table->foreignId('subcategory_id')->nullable()->after('category_id')->constrained()->nullOnDelete();
        });
    }
};
