<?php

namespace App\Actions\Chat;

use App\Models\Agent;
use App\Models\Category;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Enums\ChatMessageSender;
use App\Models\SystemSetting;
use App\Services\AI\AIServiceFactory;
use Illuminate\Support\Facades\Log;

class AnalyzeContextAction
{
    /**
     * Execute the action to analyze message context
     */
    public function execute(ChatSession $chatSession, string $messageText): array
    {
        // Get recent messages for context
        $recentMessages = $this->getRecentMessages($chatSession);

        // Build context for analysis
        $context = $this->buildContextForAnalysis($recentMessages, $messageText);

        // Use AI for analysis
        $aiAnalysisResult = $this->performAIAnalysis($context, $chatSession);

        if ($aiAnalysisResult['should_transition']) {
            Log::info('Agent transition suggested by AI analysis', [
                'chat_session_id' => $chatSession->id,
                'current_agent_id' => $chatSession->agent_id,
                'recommended_agent_id' => $aiAnalysisResult['recommended_agent']->id,
                'confidence_score' => $aiAnalysisResult['confidence_score'],
                'greeting_message' => $aiAnalysisResult['greeting_message']
            ]);
        }

        if ($aiAnalysisResult['should_recommend']) {
            Log::info('should Recommend suggested by AI analysis', [
                'should_recommend' => $aiAnalysisResult['should_recommend'],
                'recommended_category_id' => $aiAnalysisResult['recommended_category_id'] ?? null,
                'recommendation_count' => $aiAnalysisResult['recommendation_count'] ?? null
            ]);
        }

        return $aiAnalysisResult;
    }

    /**
     * Get last user message for context analysis
     */
    protected function getRecentMessages(ChatSession $chatSession): \Illuminate\Database\Eloquent\Collection
    {
        return ChatMessage::where('chat_session_id', $chatSession->id)
            ->where('sender', ChatMessageSender::USER)
            ->orderBy('created_at', 'desc')
            ->limit(1) // Only get the last user message
            ->get();
    }

    /**
     * Build context for AI analysis focusing on current message only
     */
    protected function buildContextForAnalysis($lastMessage, $currentMessage): string
    {
        // Since we're now only using the current message for analysis,
        // we'll simplify the context structure
        $context = "CURRENT USER QUERY: {$currentMessage}\n\n";

        // Add the previous message if it exists (which it might not for new chats)
        if ($lastMessage->isNotEmpty()) {
            $context .= "PREVIOUS QUERY: {$lastMessage->first()->message_text}\n\n";
        }

        return $context;
    }

    /**
     * Perform AI analysis to determine if transition is needed
     */
    protected function performAIAnalysis(string $context, ChatSession $chatSession): array
    {
        // Get all available agents
        $allAgents = Agent::where('id', '!=', $chatSession->agent_id)
            ->where('is_visible', true)
            ->get();

        if ($allAgents->isEmpty()) {
            return [
                'should_transition' => false,
                'should_recommend' => false,
                'confidence_score' => 0
            ];
        }

        $aiService = AIServiceFactory::create();

        // Build the prompt for AI analysis
        $prompt = $this->buildAgentAnalysisPrompt($context, $chatSession->agent, $allAgents);

        try {
            // Add tracking metadata for OpenAI API usage
            $trackingMetadata = [
                'action' => 'analyze_context',
                'chat_session_id' => $chatSession->id,
                'agent_id' => $chatSession->agent_id,
                'purpose' => 'agent_transition_analysis'
            ];

            $response = $aiService->generateResponse($prompt, [
                'model' => $chatSession->agent->prompt->model_type ?? 'gpt-4',
                'user_id' => $chatSession->user_id,
                'source' => 'AnalyzeContextAction',
                'action' => 'analyze_context',
                'metadata' => $trackingMetadata
            ]);

            // Parse the AI response to determine recommended agent
            return $this->parseAIResponse($response, $allAgents, $chatSession);

        } catch (\Exception $e) {
            Log::error('Error during agent transition analysis', [
                'error' => $e->getMessage(),
                'chat_session_id' => $chatSession->id
            ]);

            return [
                'should_transition' => false,
                'should_recommend' => false,
                'confidence_score' => 0
            ];
        }
    }

    /**
     * Build the prompt for agent analysis focusing on current message
     */
    protected function buildAgentAnalysisPrompt(string $context, Agent $currentAgent, $availableAgents): string
    {
        // Get the maximum number of recommendations from system settings
        $maxRecommendations = SystemSetting::getValue(
            SystemSetting::KEY_MAX_RECOMMENDATIONS,
            5
        );

        // Get categories for the current agent
        $agentCategories = Category::where('agent_id', $currentAgent->id)
            ->where('is_active', true)
            ->get();

        $prompt = "You are an Rydo's assistant that analyzes conversation context for two purposes:\n";
        $prompt .= "1. Determine if a user's request would be better handled by a different specialized Rydo agent\n";
        $prompt .= "2. Detect if the user is requesting place recommendations and identify which specific category they're interested in\n\n";

        $prompt .= "CURRENT AGENT:\n";
        $prompt .= "ID: {$currentAgent->id}\n";
        $prompt .= "Name: {$currentAgent->name}\n";
        $prompt .= "Description: {$currentAgent->description}\n\n";

        // Add categories for the current agent
        $prompt .= "AVAILABLE CATEGORIES FOR THIS AGENT:\n";
        if ($agentCategories->isNotEmpty()) {
            foreach ($agentCategories as $category) {
                $prompt .= "ID: {$category->id}, Name: {$category->name}, Description: {$category->description}\n";
            }
        } else {
            $prompt .= "No categories available for this agent.\n";
        }
        $prompt .= "\n";

        $prompt .= "AVAILABLE SPECIALIZED AGENTS:\n";
        foreach ($availableAgents as $agent) {
            $prompt .= "ID: {$agent->id}\n";
            $prompt .= "Name: {$agent->name}\n";
            $prompt .= "Description: {$agent->description}\n";
            $prompt .= "\n";
        }

        $prompt .= $context;

        $prompt .= "\nCLEAR ROUTING INSTRUCTIONS:\n";
        $prompt .= "1. Make an IMMEDIATE decision about which agent should handle this request\n";
        $prompt .= "2. Focus ONLY on the user's CURRENT QUERY, not conversation history\n";
        $prompt .= "3. Be DECISIVE - if there's any mention of a specialized domain, route to that specialist\n";
        $prompt .= "4. Only keep the conversation with the current agent if the query is CLEARLY within their domain\n\n";

        $prompt .= "\nRECOMMENDATION INSTRUCTIONS:\n";
        $prompt .= "When the user is asking for recommendations:\n";
        $prompt .= "1. IMPORTANT: Only consider categories from the provided list that belong to the current agent\n";
        $prompt .= "2. Analyze their message to determine which specific category they're interested in\n";
        $prompt .= "3. Include 'recommended_category_id' in your response with the ID of an existing category from the list\n";
        $prompt .= "4. If the user mentions a specific type of place, match it to the closest category in the provided list\n";
        $prompt .= "5. If the user asks for places 'nearby' or 'around me' without specifying a type, choose the most appropriate category from the provided list\n";
        $prompt .= "6. Only set 'recommended_category_id' to null if you cannot match to any category in the provided list\n";
        $prompt .= "7. Set 'should_recommend' to true ONLY if the user is explicitly asking for place recommendations\n";
        $prompt .= "8. Do not set 'should_recommend' to true for general questions\n";
        $prompt .= "9. If 'should_transition' is true, set 'should_recommend' to false\n";

        $prompt .= "\nRespond in JSON format with the following structure:\n";
        $prompt .= "{\n";
        $prompt .= "  \"should_transition\": boolean,\n";
        $prompt .= "  \"should_recommend\": boolean,\n";
        $prompt .= "  \"recommended_agent_id\": number or null,\n";
        $prompt .= "  \"recommended_category_id\": number or null, // ID of the category the user is interested in\n";
        $prompt .= "  \"confidence_score\": number between 0 and 1,\n";
        $prompt .= "  \"reason\": \"brief explanation\",\n";
        $prompt .= "  \"greeting_message\": \"customized message based on target agent\",\n";
        $prompt .= "  \"quick_replies\": [\"suggestion 1\", \"suggestion 2\"], // Choose 2 contextual quick replies from the suggestions\n";
        $prompt .= "  \"recommendation_count\": number or null // If should_recommend is true, specify how many recommendations to show (1-{$maxRecommendations})\n";
        $prompt .= "}\n";

        return $prompt;
    }

    /**
     * Parse the AI response
     */
    protected function parseAIResponse(string $response, $availableAgents, ChatSession $chatSession): array
    {
        try {
            // Extract JSON from response (in case there's extra text)
            preg_match('/{.*}/s', $response, $matches);

            if (empty($matches)) {
                throw new \Exception("Could not extract JSON from AI response");
            }

            $jsonResponse = json_decode($matches[0], true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Invalid JSON in AI response: " . json_last_error_msg());
            }

            // Use a very low confidence threshold to make transitions happen more eagerly
            if ($jsonResponse['should_transition'] && $jsonResponse['confidence_score'] >= 0.3) {
                // Find the recommended agent
                $recommendedAgent = $availableAgents->firstWhere('id', $jsonResponse['recommended_agent_id']);

                if (!$recommendedAgent) {
                    throw new \Exception("Recommended agent ID not found");
                }

                return [
                    'should_transition' => true,
                    'should_recommend' => false, // Always false when transitioning
                    'recommended_agent' => $recommendedAgent,
                    'confidence_score' => $jsonResponse['confidence_score'],
                    'trigger_type' => 'ai',
                    'reason' => $jsonResponse['reason'] ?? 'No reason provided',
                    'greeting_message' => $jsonResponse['greeting_message'],
                    'quick_replies' => $jsonResponse['quick_replies']
                ];
            }

            // Check if the AI recommends showing place recommendations
            if ($jsonResponse['should_recommend'] && $jsonResponse['confidence_score'] >= 0.3) {
                // Get the maximum number of recommendations from system settings
                $maxRecommendations = SystemSetting::getValue(
                    SystemSetting::KEY_MAX_RECOMMENDATIONS,
                    5
                );

                // Get the recommended category ID if available
                $recommendedCategoryId = null;
                if (isset($jsonResponse['recommended_category_id'])) {
                    // Verify that the category exists and belongs to the current agent
                    $category = Category::find($jsonResponse['recommended_category_id']);
                    if ($category && $category->agent_id === $chatSession->agent_id) {
                        $recommendedCategoryId = $category->id;

                        Log::info('Category recommendation identified', [
                            'chat_session_id' => $chatSession->id,
                            'agent_id' => $chatSession->agent_id,
                            'category_id' => $recommendedCategoryId,
                            'category_name' => $category->name
                        ]);
                    }
                }

                return [
                    'should_transition' => false,
                    'should_recommend' => true,
                    'confidence_score' => $jsonResponse['confidence_score'],
                    'trigger_type' => 'ai',
                    'reason' => $jsonResponse['reason'] ?? 'No transition needed but recommendations requested',
                    'recommendation_count' => isset($jsonResponse['recommendation_count']) ?
                        min(max((int)$jsonResponse['recommendation_count'], 1), $maxRecommendations) : null,
                    'recommended_category_id' => $recommendedCategoryId
                ];
            }

            return [
                'should_transition' => false,
                'should_recommend' => false,
                'confidence_score' => $jsonResponse['confidence_score'],
                'reason' => $jsonResponse['reason'] ?? 'No transition needed'
            ];

        } catch (\Exception $e) {
            Log::error('Error parsing AI transition response', [
                'error' => $e->getMessage(),
                'response' => $response,
                'chat_session_id' => $chatSession->id
            ]);

            return [
                'should_transition' => false,
                'should_recommend' => false,
                'confidence_score' => 0
            ];
        }
    }
}
