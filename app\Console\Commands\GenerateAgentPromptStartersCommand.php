<?php

namespace App\Console\Commands;

use App\Jobs\GenerateAgentPromptStarters;
use App\Models\Agent;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;

class GenerateAgentPromptStartersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-agent-prompt-starters {--agent= : Specific agent ID to generate prompts for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate prompt starters from OpenAI for each agent';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $specificAgentId = $this->option('agent');

        if ($specificAgentId) {
            $agent = Agent::find($specificAgentId);
            if (!$agent) {
                $this->error("Agent with ID {$specificAgentId} not found");
                return CommandAlias::FAILURE;
            }
            $this->generateForAgent($agent);
        } else {
            // Generate for all visible agents and not for general agent
            $agents = Agent::where('is_visible', true)->where('is_general',false)->get();
            foreach ($agents as $agent) {
                $this->generateForAgent($agent);
            }
        }

        $this->info('All agent prompt starter generation jobs have been dispatched.');
        return CommandAlias::SUCCESS;
    }

    /**
     * Generate prompt starters for a specific agent.
     */
    protected function generateForAgent(Agent $agent): void
    {
        $this->info("Dispatching job to generate prompt starters for agent: {$agent->name}...");
        GenerateAgentPromptStarters::dispatch($agent);
    }
}
