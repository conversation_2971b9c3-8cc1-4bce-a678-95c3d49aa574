<?php

namespace App\Actions\Places;

use App\Enums\FavoriteType;
use App\Models\Place;
use App\Models\PlaceFavorite;
use App\Models\User;

class GetPlaceFavoriteTypeAction
{
    /**
     * Get the favorite type for a place
     *
     * @param User $user
     * @param Place $place
     * @return FavoriteType|null
     */
    public function execute(User $user, Place $place): ?FavoriteType
    {
        $favorite = PlaceFavorite::where('user_id', $user->id)
            ->where('place_id', $place->id)
            ->first();

        return $favorite ? $favorite->type : null;
    }
}
