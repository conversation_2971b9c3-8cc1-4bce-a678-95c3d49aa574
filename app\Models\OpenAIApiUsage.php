<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OpenAIApiUsage extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'openai_api_usages';

    protected $fillable = [
        'user_id',
        'chat_message_id',
        'chat_session_id',
        'model',
        'source',
        'action',
        'request_type',
        'endpoint',
        'prompt_tokens',
        'completion_tokens',
        'total_tokens',
        'prompt_cost',
        'completion_cost',
        'total_cost',
        'metadata',
    ];

    protected $casts = [
        'prompt_tokens' => 'integer',
        'completion_tokens' => 'integer',
        'total_tokens' => 'integer',
        'prompt_cost' => 'decimal:6',
        'completion_cost' => 'decimal:6',
        'total_cost' => 'decimal:6',
        'metadata' => 'json',
    ];

    /**
     * Get the user that owns the API usage record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the chat message associated with this API usage.
     */
    public function chatMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class);
    }

    /**
     * Get the chat session associated with this API usage.
     */
    public function chatSession(): BelongsTo
    {
        return $this->belongsTo(ChatSession::class);
    }

    /**
     * Scope a query to only include records from a specific date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include records for a specific model.
     */
    public function scopeForModel($query, $model)
    {
        return $query->where('model', $model);
    }

    /**
     * Scope a query to only include records from a specific source.
     */
    public function scopeFromSource($query, $source)
    {
        return $query->where('source', $source);
    }
}
