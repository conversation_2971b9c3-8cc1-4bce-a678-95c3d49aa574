<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status->value,
            'title' => $this->status->value === 'active' ? 'Current Plan' : 'Previous  Plan',
            'start_date' => $this->start_date->toISOString(),
            'end_date' => $this->end_date->format('M d Y'),
            'purchase_date' => $this->purchase_date?->toISOString(),
            'auto_renewal' => $this->auto_renewal ?? true,
            'platform_type' => $this->platform_type,
            'product_id' => $this->product_id,
            'transaction_id' => $this->transaction_id,
            'days_until_renewal' => $this->getDaysUntilRenewal(),
            'cancellation_date' => $this->cancellation_date?->toISOString(),
            'days_until_access_ends' => $this->getDaysUntilAccessEnds(),
            'is_reactivation' => $this->isReactivation(),
            'plan' => $this->whenLoaded('planDetails', function () {
                return  SubscriptionPlanResource::make($this->planDetails);
            }),
        ];
    }
}
