<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChatSessionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     * @mixin ChatSession
     */
    public function toArray(Request $request): array
    {
    
        return [
            'id' => $this->id,
            'agent_id' => $this->agent_id,
            'agent_name' => $this->agent->name ?? '',
            'title' => $this->title ?? 'New Conversation',
            'timestamp' => $this->last_message_at,
            'avatar_url' => $this->agent->agent_avatar ?? '',
        ];
    }
}
