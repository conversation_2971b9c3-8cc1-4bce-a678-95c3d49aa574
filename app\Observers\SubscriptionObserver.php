<?php

namespace App\Observers;

use App\Enums\SubscriptionStatus;
use App\Models\Subscription;
use Illuminate\Support\Facades\Log;

class SubscriptionObserver
{
    /**
     * Handle the Subscription "created" event.
     */
    public function created(Subscription $subscription): void
    {
        // When a subscription is created with ACTIVE status, ensure user is premium
        if ($subscription->status === SubscriptionStatus::ACTIVE && $subscription->isActive()) {
            $user = $subscription->user;
            if (!$user->is_premium) {
                $user->update(['is_premium' => true]);
            }
        }
    }

    /**
     * Handle the Subscription "updated" event.
     */
    public function updated(Subscription $subscription): void
    {
        $user = $subscription->user;
        
        // Check if status was changed
        if ($subscription->wasChanged('status')) {
            $oldStatus = $subscription->getOriginal('status');
            $newStatus = $subscription->status;
            // Handle status changes
            match ($newStatus) {
                SubscriptionStatus::ACTIVE => $this->handleActivation($subscription),
                SubscriptionStatus::CANCELED => $this->handleCancellation($subscription),
                SubscriptionStatus::EXPIRED => $this->handleExpiration($subscription),
            };
        }
        
        // Check if end_date was changed and subscription is now expired
        if ($subscription->wasChanged('end_date') && !$subscription->isActive()) {
            $this->handleExpiration($subscription);
        }
    }

    /**
     * Handle subscription activation
     */
    private function handleActivation(Subscription $subscription): void
    {
        $user = $subscription->user;
        
        // Only update if subscription is truly active (not expired)
        if ($subscription->isActive() && !$user->is_premium) {
            $user->update(['is_premium' => true]);
        }
    }

    /**
     * Handle subscription cancellation
     */
    private function handleCancellation(Subscription $subscription): void
    {
        $user = $subscription->user;
        
        // Check if user has any other active subscriptions
        $hasOtherActiveSubscription = $user->subscriptions()
            ->where('id', '!=', $subscription->id)
            ->where('status', SubscriptionStatus::ACTIVE)
            ->where('end_date', '>', now())
            ->exists();
        
        // Only set to non-premium if no other active subscriptions
        if (!$hasOtherActiveSubscription && $user->is_premium) {
            $user->update(['is_premium' => false]);
        }
    }

    /**
     * Handle subscription expiration
     */
    private function handleExpiration(Subscription $subscription): void
    {
        $user = $subscription->user;
        
        // Check if user has any other active subscriptions
        $hasOtherActiveSubscription = $user->subscriptions()
            ->where('id', '!=', $subscription->id)
            ->where('status', SubscriptionStatus::ACTIVE)
            ->where('end_date', '>', now())
            ->exists();
        
        // Only set to non-premium if no other active subscriptions
        if (!$hasOtherActiveSubscription && $user->is_premium) {
            $user->update(['is_premium' => false]);
        }
    }

    /**
     * Handle the Subscription "deleted" event.
     */
    public function deleted(Subscription $subscription): void
    {
        $user = $subscription->user;
        
        // Check if user has any other active subscriptions
        $hasOtherActiveSubscription = $user->subscriptions()
            ->where('id', '!=', $subscription->id)
            ->where('status', SubscriptionStatus::ACTIVE)
            ->where('end_date', '>', now())
            ->exists();
        
        // Only set to non-premium if no other active subscriptions
        if (!$hasOtherActiveSubscription && $user->is_premium) {
            $user->update(['is_premium' => false]);
            
            Log::info('User premium status deactivated via subscription observer', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'action' => 'deletion',
                'is_premium' => false,
            ]);
        }
    }
}
