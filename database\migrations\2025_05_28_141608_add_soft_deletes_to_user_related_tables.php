<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add soft deletes to social_providers table
        Schema::table('social_providers', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to user_agent_preferences table
        Schema::table('user_agent_preferences', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to chat_sessions table
        Schema::table('chat_sessions', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to message_reactions table
        Schema::table('message_reactions', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to message_reports table
        Schema::table('message_reports', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to payment_history table
        Schema::table('payment_history', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to recommendations table
        Schema::table('recommendations', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to feature_usage_statistics table
        Schema::table('feature_usage_statistics', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to device_info table
        Schema::table('device_info', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to place_favorites table
        Schema::table('place_favorites', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add soft deletes to user_recommendation_usage table
        Schema::table('user_recommendation_usage', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove soft deletes from social_providers table
        Schema::table('social_providers', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from user_agent_preferences table
        Schema::table('user_agent_preferences', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from chat_sessions table
        Schema::table('chat_sessions', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from message_reactions table
        Schema::table('message_reactions', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from message_reports table
        Schema::table('message_reports', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from subscriptions table
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from payment_history table
        Schema::table('payment_history', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from recommendations table
        Schema::table('recommendations', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from feature_usage_statistics table
        Schema::table('feature_usage_statistics', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from device_info table
        Schema::table('device_info', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from place_favorites table
        Schema::table('place_favorites', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove soft deletes from user_recommendation_usage table
        Schema::table('user_recommendation_usage', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
