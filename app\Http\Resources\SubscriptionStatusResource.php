<?php

namespace App\Http\Resources;

use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;

class SubscriptionStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = $this->resource;
        $currentSubscription = Subscription::getCurrentSubscription($user->id);
        $lastSubscription = Subscription::getLastSubscription($user->id);

        $data = [
            'has_active_subscription' => $currentSubscription !== null,
            'subscription' => $currentSubscription ? SubscriptionResource::make($currentSubscription) : null,
        ];

        // Add subscription history for reactivation
        if (!$currentSubscription && $lastSubscription) {
            $data['subscription_history'] = [
                'has_previous_subscription' => true,
                'can_reactivate' => $lastSubscription->canReactivate(),
                'last_subscription' => [
                    'plan' => $lastSubscription->plan->value,
                    'status' => $lastSubscription->status->value,
                    'ended_at' => $lastSubscription->end_date->toISOString(),
                    'was_auto_renewal' => $lastSubscription->auto_renewal ?? true,
                    'was_cancelled' => $lastSubscription->isCancelled(),
                ]
            ];
        }

        // Add cancellation info for cancelled subscriptions
        if ($currentSubscription && $currentSubscription->isCancelled()) {
            $data['cancellation_info'] = [
                'is_cancelled' => true,
                'cancellation_date' => $currentSubscription->cancellation_date->toISOString(),
                'days_until_access_ends' => $currentSubscription->getDaysUntilAccessEnds(),
            ];
        }

        // Add available plan info if no active subscription
        if (!$currentSubscription) {
            $rydoPlusPlan = Plan::where('code', 'RYDO_PLUS')->where('is_active', true)->first();
            if ($rydoPlusPlan) {
                $data['available_plan'] = SubscriptionPlanResource::make($rydoPlusPlan);
            }
        }

        return $data;
    }

}
