<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserAgentPreferenceBulkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'interests' => 'required|array|min:1',
            'interests.*.agent_id' => 'required|exists:agents,id',
            'interests.*.interest_level' => 'required|integer|min:1|max:5',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'interests.required' => 'The interests array is required.',
            'interests.array' => 'The interests must be an array.',
            'interests.min' => 'At least one interest must be provided.',
            'interests.*.agent_id.required' => 'Each interest must have an agent ID.',
            'interests.*.agent_id.exists' => 'One or more selected agents do not exist.',
            'interests.*.interest_level.required' => 'Each interest must have an interest level.',
            'interests.*.interest_level.integer' => 'Interest level must be an integer.',
            'interests.*.interest_level.min' => 'Interest level must be at least 1.',
            'interests.*.interest_level.max' => 'Interest level must not be greater than 5.',
        ];
    }
}
