<?php

namespace Tests\Feature\Auth;

use App\Events\UserDeleted;
use App\Models\User;
use App\Models\SocialProvider;
use App\Models\UserAgentPreference;
use App\Models\ChatSession;
use App\Models\MessageReaction;
use App\Models\MessageReport;
use App\Models\Subscription;
use App\Models\PaymentHistory;
use App\Models\Recommendation;
use App\Models\FeatureUsageStatistic;
use App\Models\DeviceInfo;
use App\Models\PlaceFavorite;
use App\Models\UserRecommendationUsage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class DeleteAccountTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user
        User::factory()->create([
            'user_type' => 'admin',
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test that an unauthenticated user cannot delete their account.
     */
    public function test_unauthenticated_user_cannot_delete_account(): void
    {
        $response = $this->deleteJson('/api/account');

        $response->assertStatus(401);
    }

    /**
     * Test that an authenticated user can delete their account.
     */
    public function test_authenticated_user_can_delete_account(): void
    {
        Event::fake();

        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->deleteJson('/api/account');

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Account deleted successfully'
            ]);

        // Assert the event was dispatched
        Event::assertDispatched(UserDeleted::class, function ($event) use ($user) {
            return $event->user->id === $user->id;
        });

        // Verify the user was soft deleted
        $this->assertSoftDeleted($user);
    }

    /**
     * Test that all related records are soft deleted when a user deletes their account.
     */
    public function test_all_related_records_are_soft_deleted(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create related records
        $socialProvider = SocialProvider::factory()->create(['user_id' => $user->id]);
        $agentPreference = UserAgentPreference::factory()->create(['user_id' => $user->id]);
        $chatSession = ChatSession::factory()->create(['user_id' => $user->id]);

        // Create a chat message first for the reactions and reports
        $chatMessage = \App\Models\ChatMessage::factory()->create([
            'chat_session_id' => $chatSession->id,
            'user_id' => $user->id
        ]);

        $messageReaction = MessageReaction::factory()->create([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessage->id,
            'agent_id' => $chatMessage->agent_id,
            'reaction' => 'like'
        ]);
        $messageReport = MessageReport::factory()->create([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessage->id,
            'agent_id' => $chatMessage->agent_id
        ]);
        $subscription = Subscription::factory()->create(['user_id' => $user->id]);
        $paymentHistory = PaymentHistory::factory()->create(['user_id' => $user->id]);
        $recommendation = Recommendation::factory()->create([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessage->id,
            'agent_id' => $chatMessage->agent_id
        ]);
        $featureUsage = FeatureUsageStatistic::factory()->create(['user_id' => $user->id]);
        $deviceInfo = DeviceInfo::factory()->create(['user_id' => $user->id]);
        $placeFavorite = PlaceFavorite::factory()->create(['user_id' => $user->id]);
        $recommendationUsage = UserRecommendationUsage::factory()->create(['user_id' => $user->id]);

        $response = $this->deleteJson('/api/account');

        $response->assertStatus(200);

        // Verify the user was soft deleted (only User model has soft deletes)
        $this->assertSoftDeleted($user);

        // Verify all related records were deleted (most models don't have soft deletes)
        $this->assertDatabaseMissing('social_providers', ['id' => $socialProvider->id]);
        $this->assertDatabaseMissing('user_agent_preferences', ['id' => $agentPreference->id]);
        $this->assertDatabaseMissing('chat_sessions', ['id' => $chatSession->id]);
        $this->assertDatabaseMissing('message_reactions', ['id' => $messageReaction->id]);
        $this->assertDatabaseMissing('message_reports', ['id' => $messageReport->id]);
        $this->assertDatabaseMissing('subscriptions', ['id' => $subscription->id]);
        $this->assertDatabaseMissing('payment_history', ['id' => $paymentHistory->id]);
        $this->assertDatabaseMissing('recommendations', ['id' => $recommendation->id]);
        $this->assertDatabaseMissing('feature_usage_statistics', ['id' => $featureUsage->id]);
        $this->assertDatabaseMissing('device_info', ['id' => $deviceInfo->id]);
        $this->assertDatabaseMissing('place_favorites', ['id' => $placeFavorite->id]);
        $this->assertDatabaseMissing('user_recommendation_usage', ['id' => $recommendationUsage->id]);
    }

    /**
     * Test that the user's email is changed after account deletion.
     */
    public function test_user_email_is_changed_after_deletion(): void
    {
        Event::fake();

        $user = User::factory()->create();
        $originalEmail = $user->email;
        Sanctum::actingAs($user);

        $response = $this->deleteJson('/api/account');

        $response->assertStatus(200);

        // Verify the email was changed
        $this->assertNotEquals($originalEmail, $user->fresh()->email);
        $this->assertStringStartsWith($originalEmail, $user->fresh()->email);
        $this->assertStringEndsWith('@deleted.com', $user->fresh()->email);
    }

    /**
     * Test that all user tokens are revoked after account deletion.
     */
    public function test_all_user_tokens_are_revoked_after_deletion(): void
    {
        Event::fake();

        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create multiple tokens for the user
        $token1 = $user->createToken('test-token-1')->plainTextToken;
        $token2 = $user->createToken('test-token-2')->plainTextToken;
        $token3 = $user->createToken('test-token-3')->plainTextToken;

        $response = $this->deleteJson('/api/account');

        $response->assertStatus(200);

        // Verify that all tokens were revoked
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'tokenable_type' => User::class,
        ]);
    }
}
