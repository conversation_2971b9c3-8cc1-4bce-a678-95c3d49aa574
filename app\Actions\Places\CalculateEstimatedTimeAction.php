<?php

namespace App\Actions\Places;

use App\Models\Place;

class CalculateEstimatedTimeAction
{
    /**
     * Calculate the estimated travel time from user location to place
     *
     * @param float|null $userLat User's latitude
     * @param float|null $userLng User's longitude
     * @param Place $place The place to calculate time to
     * @return string Formatted estimated time (e.g. "15m", "1.2h")
     */
    public function execute(?float $userLat, ?float $userLng, Place $place): string
    {
        if ($userLat === null || $userLng === null) {
            // Return empty string when location is not available
            // This will be handled by the frontend to hide the ETA or show a message
            return '';
        }

        $distance = $this->calculateDistance($userLat, $userLng, $place->latitude, $place->longitude);

        // Assuming average speed of 30 km/h in city traffic
        $timeInHours = $distance / 30;

        return $this->formatTime($timeInHours);
    }

    /**
     * Calculate distance between two points using Haversine formula
     *
     * @param float $lat1 First point latitude
     * @param float $lng1 First point longitude
     * @param float $lat2 Second point latitude
     * @param float $lng2 Second point longitude
     * @return float Distance in kilometers
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        // Earth's radius in kilometers
        $earthRadius = 6371;

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earthRadius * $c;

        return $distance;
    }

    /**
     * Format time in hours to a human-readable string
     *
     * @param float $timeInHours Time in hours
     * @return string Formatted time string
     */
    private function formatTime(float $timeInHours): string
    {
        if ($timeInHours < 1) {
            $minutes = round($timeInHours * 60);
            $minutes = max(1, $minutes);
            return "{$minutes}m";
        }

        return number_format($timeInHours, 1) . 'h';
    }
}
