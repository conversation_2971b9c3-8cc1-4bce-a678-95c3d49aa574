<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum PlacePersona: string implements HasColor, HasLabel
{
    case ADVENTURE = 'adventure';
    case BITES = 'bites';
    case NEEDS = 'needs';
    case VENTURES = 'ventures';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function getColor(): string|array|null
    {
        return match($this) {
            self::ADVENTURE => 'success',
            self::BITES => 'warning',
            self::NEEDS => 'danger',
            self::VENTURES => 'info',
        };
    }

    public function getLabel(): ?string
    {
        return match($this) {
            self::ADVENTURE => 'Adventure',
            self::BITES => 'Bites',
            self::NEEDS => 'Needs',
            self::VENTURES => 'Ventures',
        };
    }
}