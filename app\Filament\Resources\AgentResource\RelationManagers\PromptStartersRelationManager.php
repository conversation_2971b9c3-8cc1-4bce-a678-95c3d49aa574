<?php

namespace App\Filament\Resources\AgentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\AgentPromptStarter;

class PromptStartersRelationManager extends RelationManager
{
    protected static string $relationship = 'agentPromptStarters';

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?string $title = 'Prompt Starters';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('is_active')
                    ->default(true)
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
                Tables\Actions\Action::make('generate')
                    ->label('Generate Starters')
                    ->icon('heroicon-o-sparkles')
                    ->color('success')
                    ->action(function (RelationManager $livewire) {
                        $agent = $livewire->getOwnerRecord();
                        \App\Jobs\GenerateAgentPromptStarters::dispatch($agent);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Generating Prompt Starters')
                            ->body('Prompt starters are being generated for this agent. This may take a moment.')
                            ->success()
                            ->send();
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('toggleActive')
                        ->label('Toggle Active Status')
                        ->icon('heroicon-o-check-circle')
                        ->action(function (Tables\Actions\BulkAction $action, array $data) {
                            $status = $data['status'] ?? true;
                            
                            $action->getRecords()->each(function ($record) use ($status) {
                                $record->update(['is_active' => $status]);
                            });
                            
                            \Filament\Notifications\Notification::make()
                                ->title('Status Updated')
                                ->body('The active status has been updated for the selected prompt starters.')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Toggle::make('status')
                                ->label('Active Status')
                                ->default(true),
                        ]),
                ]),
            ]);
    }
}
