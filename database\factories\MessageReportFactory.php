<?php

namespace Database\Factories;

use App\Models\MessageReport;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MessageReport>
 */
class MessageReportFactory extends Factory
{
    protected $model = MessageReport::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'chat_message_id' => $this->faker->numberBetween(1, 100),
            'agent_id' => $this->faker->numberBetween(1, 10),
            'report_reason' => $this->faker->sentence,
        ];
    }
}
