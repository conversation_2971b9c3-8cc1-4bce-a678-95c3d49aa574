<?php

namespace App\Enums;

enum ContentSectionType: string
{
    case HOMEPAGE = 'homepage';
    case HERO = 'hero';
    case FEATURES = 'features';
    case TESTIMONIALS = 'testimonials';
    case CTA = 'cta';
    case CONTENT = 'content';

    public function label(): string
    {
        return match($this) {
            self::HOMEPAGE => 'Homepage Section',
            self::HERO => 'Hero Section',
            self::FEATURES => 'Features',
            self::TESTIMONIALS => 'Testimonials',
            self::CTA => 'Call to Action',
            self::CONTENT => 'Content Block',
        };
    }

    public static function options(): array
    {
        return collect(self::cases())->mapWithKeys(fn ($type) => [
            $type->value => $type->label()
        ])->all();
    }
} 