<?php

namespace App\Enums;

enum SubscriptionStatus: string
{
    case ACTIVE = 'active';
    case EXPIRED = 'expired';
    case CANCELED = 'canceled';

    public function getColor(): string
    {
        return match($this) {
            self::ACTIVE => 'success',
            self::EXPIRED => 'danger',
            self::CANCELED => 'warning',
        };
    }

    public function getLabel(): string
    {
        return match($this) {
            self::ACTIVE => 'Active',
            self::EXPIRED => 'Expired',
            self::CANCELED => 'Cancelled',
        };
    }
} 