<?php

namespace App\Observers;

use App\Actions\Chat\GenerateChatTitleAction;
use App\Enums\ChatMessageSender;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\App;

class ChatMessageObserver
{
    /**
     * Handle the ChatMessage "created" event.
     */
    public function created(ChatMessage $chatMessage): void
    {
        // Only generate a title after an agent message is created
        if ($chatMessage->sender === ChatMessageSender::AGENT) {
            $chatSession = $chatMessage->chatSession;
            
            if (!$chatSession->title || $chatSession->title === 'New Conversation') {
                $messageCount = $chatSession->messages()->count();
                
                if ($messageCount >= 2) {
                    $generateTitleAction = App::make(GenerateChatTitleAction::class);
                    $generateTitleAction->execute($chatSession);
                }
            }
        }
    }
}
