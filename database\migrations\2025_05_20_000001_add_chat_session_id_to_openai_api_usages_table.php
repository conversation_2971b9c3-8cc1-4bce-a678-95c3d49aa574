<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('openai_api_usages', function (Blueprint $table) {
            $table->foreignId('chat_session_id')->nullable()->after('chat_message_id')->constrained()->nullOnDelete();
            
            // Add index for faster queries
            $table->index(['chat_session_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('openai_api_usages', function (Blueprint $table) {
            $table->dropForeign(['chat_session_id']);
            $table->dropIndex(['chat_session_id', 'created_at']);
            $table->dropColumn('chat_session_id');
        });
    }
};
