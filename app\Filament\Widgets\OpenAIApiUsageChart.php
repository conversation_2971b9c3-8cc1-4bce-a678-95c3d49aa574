<?php

namespace App\Filament\Widgets;

use App\Models\OpenAIApiUsage;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class OpenAIApiUsageChart extends ChartWidget
{
    protected static ?string $heading = 'OpenAI API Usage (Last 30 Days)';

    protected static ?string $pollingInterval = '60s';

    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $data = OpenAIApiUsage::query()
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get([
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_tokens) as total_tokens'),
                DB::raw('SUM(total_cost) as total_cost'),
            ])
            ->keyBy('date')
            ->map(function ($item) {
                return [
                    'tokens' => $item->total_tokens,
                    'cost' => $item->total_cost,
                ];
            });

        // Fill in missing dates with zeros
        $dates = collect();
        for ($i = 0; $i < 30; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dates->put($date, $date);
        }

        $filledData = $dates->map(function ($date) use ($data) {
            return $data[$date] ?? ['tokens' => 0, 'cost' => 0];
        })->sortKeys();

        return [
            'datasets' => [
                [
                    'label' => 'Total Tokens',
                    'data' => $filledData->pluck('tokens')->toArray(),
                    'borderColor' => '#9061F9',
                    'backgroundColor' => 'rgba(144, 97, 249, 0.1)',
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Total Cost (USD)',
                    'data' => $filledData->pluck('cost')->toArray(),
                    'borderColor' => '#0EA5E9',
                    'backgroundColor' => 'rgba(14, 165, 233, 0.1)',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $filledData->keys()->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'tooltip' => [
                    'callbacks' => [
                        'label' => "function(context) {
                            if (context.dataset.yAxisID === 'y1') {
                                return context.dataset.label + ': $' + context.parsed.y.toFixed(6);
                            }
                            return context.dataset.label + ': ' + context.parsed.y.toLocaleString();
                        }",
                    ],
                ],
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Tokens',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Cost (USD)',
                    ],
                    'ticks' => [
                        'callback' => "function(value) { return '$' + value.toFixed(6); }",
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
        ];
    }
}
