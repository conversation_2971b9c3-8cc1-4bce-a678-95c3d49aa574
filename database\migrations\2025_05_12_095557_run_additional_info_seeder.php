<?php

use Database\Seeders\AdditionalInfoSeeder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This will run the AdditionalInfoSeeder to populate the additional_infos table
     * with predefined tags.
     */
    public function up(): void
    {
        // Create a new instance of the AdditionalInfoSeeder
        $seeder = new AdditionalInfoSeeder();

        // Run the seeder
        $seeder->run();
    }

    /**
     * Reverse the migrations.
     * We don't need to do anything here as the additional info tags can be
     * recreated by running the migration again.
     */
    public function down(): void
    {
        // No action needed for rollback
    }
};
