<?php

namespace Database\Factories;

use App\Models\Recommendation;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Recommendation>
 */
class RecommendationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'old_place_id' => $this->faker->numberBetween(1, 100),
            'places_ids' => json_encode([$this->faker->numberBetween(1, 100)]),
            'chat_message_id' => $this->faker->numberBetween(1, 100),
            'agent_id' => $this->faker->numberBetween(1, 10),
            'credit_cost' => $this->faker->randomFloat(2, 0, 10),
        ];
    }
}
