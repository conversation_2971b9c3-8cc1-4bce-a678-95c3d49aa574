<?php

namespace App\Filament\Resources\ContentSectionResource\Pages;

use App\Filament\Resources\ContentSectionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\Grid;

class ViewContentSection extends ViewRecord
{
    protected static string $resource = ContentSectionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        TextEntry::make('title'),
                        TextEntry::make('slug'),
                        TextEntry::make('type'),
                        TextEntry::make('order'),
                    ])->columns(2),

                Section::make('Content')
                    ->schema([
                        KeyValueEntry::make('content')
                            ->columnSpanFull(),
                    ]),

                Section::make('Settings')
                    ->schema([
                        KeyValueEntry::make('settings')
                            ->columnSpanFull(),
                    ]),

                Section::make('Visibility')
                    ->schema([
                        TextEntry::make('is_active')
                            ->badge()
                            ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
                        TextEntry::make('start_at')
                            ->dateTime(),
                        TextEntry::make('end_at')
                            ->dateTime(),
                    ])->columns(3),
            ]);
    }
} 