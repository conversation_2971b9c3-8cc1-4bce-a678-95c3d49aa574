<?php

namespace App\Actions\Langfuse;

use App\Models\ChatSession;
use App\Services\Langfuse\LangfuseTrackingService;
use Illuminate\Support\Facades\Log;

class TrackChatSessionAction
{
    protected LangfuseTrackingService $langfuseTracking;

    public function __construct(LangfuseTrackingService $langfuseTracking)
    {
        $this->langfuseTracking = $langfuseTracking;
    }

    /**
     * Track a chat session in Langfuse
     *
     * @param ChatSession $chatSession
     * @return string|null The Langfuse session ID
     */
    public function execute(ChatSession $chatSession): ?string
    {
        try {
            Log::info('Tracking chat session in Langfuse', [
                'chat_session_id' => $chatSession->id,
                'user_id' => $chatSession->user_id,
                'agent_id' => $chatSession->agent_id,
            ]);

            $sessionId = $this->langfuseTracking->trackChatSession($chatSession);

            if ($sessionId) {
                Log::info('Successfully tracked chat session in Langfuse', [
                    'chat_session_id' => $chatSession->id,
                    'langfuse_session_id' => $sessionId,
                ]);
            }

            return $sessionId;
        } catch (\Exception $e) {
            Log::error('Failed to track chat session in Langfuse', [
                'chat_session_id' => $chatSession->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }
}
