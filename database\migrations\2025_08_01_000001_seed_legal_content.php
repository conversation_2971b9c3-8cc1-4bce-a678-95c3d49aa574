<?php

use Database\Seeders\LegalContentSeeder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This migration runs the LegalContentSeeder to populate the system_settings table with legal content
     */
    public function up(): void
    {
        if (Schema::hasTable('system_settings')) {
            (new LegalContentSeeder())->run();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('system_settings')) {
            DB::table('system_settings')
                ->whereIn('key', ['privacy_policy', 'terms_and_conditions'])
                ->delete();
        }
    }
};
