<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SettingsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'title' => $this->getTitle(),
            'key' => $this->key,
            'type' => $this->type,
            'value' => $this->getValue(),
        ];

        return $data;
    }

    /**
     * Get a human-readable title for the setting
     * 
     * @return string
     */
    protected function getTitle(): string
    {
        if (!empty($this->description)) {
            return $this->description;
        }

        return ucwords(str_replace('_', ' ', $this->key));
    }

    /**
     * Get the value based on the setting type
     * 
     * @return mixed
     */
    protected function getValue(): mixed
    {
        if ($this->type === 'editor') {
            return url("api/settings/{$this->key}");
        }

        return $this->value;
    }
}
