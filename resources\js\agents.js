document.addEventListener('DOMContentLoaded', function() {
    const agentButtons = document.querySelectorAll('.agent-switcher-btn');
    const agentItems = document.querySelectorAll('.agent-display .item');
    const agentDisplay = document.querySelector('.agent-display');
    let currentIndex = 0;
    let autoSwitchInterval;

    function switchToAgent(index) {
        // Remove active class from all buttons and items
        agentButtons.forEach(btn => btn.classList.remove('active'));
        agentItems.forEach(item => item.classList.remove('active'));
        
        // Add active class to button and corresponding item
        agentButtons[index].classList.add('active');
        agentItems[index].classList.add('active');
        currentIndex = index;
    }

    function startAutoSwitch() {
        if (!autoSwitchInterval) {
            autoSwitchInterval = setInterval(() => {
                currentIndex = (currentIndex + 1) % agentButtons.length;
                switchToAgent(currentIndex);
            }, 4000);
        }
    }

    function stopAutoSwitch() {
        clearInterval(autoSwitchInterval);
        autoSwitchInterval = null;
    }

    agentButtons.forEach((button, index) => {
        button.addEventListener('click', function() {
            stopAutoSwitch();
            switchToAgent(index);
            startAutoSwitch();
        });
    });

    // Add hover events to pause/resume auto-switching
    agentDisplay.addEventListener('mouseenter', stopAutoSwitch);
    agentDisplay.addEventListener('mouseleave', startAutoSwitch);

    // Start auto-switching when page loads
    startAutoSwitch();
}); 