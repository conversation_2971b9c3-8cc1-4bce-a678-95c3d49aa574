<?php

namespace App\Filament\Resources\OpenAIApiUsageResource\Pages;

use App\Filament\Resources\OpenAIApiUsageResource;
use App\Models\OpenAIApiUsage;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListOpenAIApiUsages extends ListRecords
{
    protected static string $resource = OpenAIApiUsageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No header actions needed for this resource
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // Add widgets here if needed
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Records'),
            'today' => Tab::make('Today')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereDate('created_at', today())),
            'this_week' => Tab::make('This Week')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])),
            'this_month' => Tab::make('This Month')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereMonth('created_at', now()->month)->whereYear('created_at', now()->year)),
        ];
    }

    protected function getTableEmptyStateHeading(): string
    {
        return 'No OpenAI API usage records found';
    }

    protected function getTableEmptyStateDescription(): string
    {
        return 'Once you start using OpenAI API, usage records will appear here.';
    }
}
