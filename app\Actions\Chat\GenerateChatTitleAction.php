<?php

namespace App\Actions\Chat;

use App\Enums\ModelType;
use App\Models\ChatSession;
use App\Services\AI\AIServiceFactory;
use Illuminate\Support\Facades\Log;

class GenerateChatTitleAction
{
    /**
     * Generate a title for a chat session based on its messages
     *
     * @param ChatSession $chatSession
     * @return string|null
     */
    public function execute(ChatSession $chatSession): ?string
    {
        try {
            // Get the last user message and agent response
            $lastUserMessage = $chatSession->messages()
                ->where('sender', 'user')
                ->orderBy('created_at', 'desc')
                ->first();

            $lastAgentMessage = $chatSession->messages()
                ->where('sender', 'agent')
                ->orderBy('created_at', 'desc')
                ->first();

            // If we don't have both a user message and an agent response, return default title
            if (!$lastAgentMessage || !$lastAgentMessage) {
                return 'New Conversation';
            }

            // Create the prompt for title generation
            $prompt = $this->buildTitleGenerationPrompt($lastUserMessage->message_text, $lastAgentMessage->message_text);

            // Get AI service
            $aiService = AIServiceFactory::create();

            // Add tracking metadata for OpenAI API usage
            $trackingMetadata = [
                'action' => 'generate_chat_title',
                'chat_session_id' => $chatSession->id,
                'purpose' => 'title_generation'
            ];

            // Generate title
            $response = $aiService->generateResponse($prompt, [
                'model' => ModelType::GPT_4o_MINI->value,
                'temperature' => 0.7,
                'max_tokens' => 20,
                'user_id' => $chatSession->user_id,
                'source' => 'GenerateChatTitleAction',
                'action' => 'generate_chat_title',
                'metadata' => $trackingMetadata
            ]);
            Log::info('Generated title: ' . $response);

            // Clean up the response (remove quotes, special characters, etc.)
            $title = $this->cleanTitle($response);

            // Update the chat session with the new title
            $chatSession->update(['title' => $title]);

            return $title;
        } catch (\Exception $e) {
            Log::error('Failed to generate chat title: ' . $e->getMessage());
            return 'New Conversation';
        }
    }

    /**
     * Build the prompt for title generation
     *
     * @param string $userMessage
     * @param string $agentResponse
     * @return string
     */
    private function buildTitleGenerationPrompt(string $userMessage, string $agentResponse): string
    {
        return <<<EOT
You are a helpful assistant that generates short, descriptive titles for chat conversations.

Instructions:
- Generate a short, descriptive title (maximum 4 words)
- The title should capture the essence of the conversation
- Avoid quotes and special characters
- Be concise and clear

User's message:
{$userMessage}

Agent's response:
{$agentResponse}

Generate a title:
EOT;
    }

    /**
     * Clean up the generated title
     *
     * @param string $title
     * @return string
     */
    private function cleanTitle(string $title): string
    {
        // Remove quotes, extra spaces, and limit to 50 characters
        $title = trim(str_replace(['"', "'"], '', $title));
        return substr($title, 0, 50);
    }
}
