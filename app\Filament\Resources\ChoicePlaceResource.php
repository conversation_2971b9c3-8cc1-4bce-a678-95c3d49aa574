<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ChoicePlaceResource\Pages;
use App\Models\Place;
use App\Models\Category;
use App\Models\Agent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Redirect;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Checkbox;
use Tapp\FilamentGoogleAutocomplete\Forms\Components\GoogleAutocomplete;

class ChoicePlaceResource extends Resource
{
    protected static ?string $model = Place::class;
    protected static ?string $navigationIcon = 'heroicon-o-star';
    protected static ?string $navigationLabel = 'Rydo Choices';
    protected static ?string $navigationGroup = 'Places Management';
    protected static ?int $navigationSort = 2;
    protected static ?string $recordTitleAttribute = 'name';
    protected static ?string $modelLabel = 'Rydo Choice';
    protected static ?string $pluralModelLabel = 'Rydo Choices';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('is_choice', true);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Location Information Section
                Forms\Components\Section::make('Location Information')
                    ->description('Search and set the place location details')
                    ->schema([
                        GoogleAutocomplete::make('google_search')
                            ->label('Search Places')
                            ->countries(['AE']) // Restrict to UAE
                            ->withFields([
                                TextInput::make('name')
                                    ->required(),
                                TextInput::make('address')
                                    ->extraInputAttributes([
                                        'data-google-field' => '{street_number} {route}, {sublocality_level_1}',
                                    ])->required(),
                                TextInput::make('latitude')
                                    ->required()
                                    ->numeric(),
                                TextInput::make('longitude')
                                    ->required()
                                    ->numeric(),
                                TextInput::make('place_id')
                                    ->required()
                                    ->unique(ignoreRecord: true),
                                TextInput::make('phone_number')
                                    ->label('Phone Number')
                                    ->extraInputAttributes([
                                        'data-google-field' => '{international_phone_number}',
                                    ]),
                                TextInput::make('website')
                                    ->label('Website')
                                    ->extraInputAttributes([
                                        'data-google-field' => '{website}',
                                    ]),

                            ])
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $set('source', 'google_places');
                                }
                            })
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                // Basic Information Section
                Forms\Components\Section::make('Basic Information')
                    ->description('Set the place details and categorization')
                    ->schema([
                        Select::make('category_id')
                            ->label('Category')
                            ->options(Category::pluck('name', 'id')),
                        Select::make('agent_id')
                            ->label('Agent')
                            ->options(Agent::where('is_visible', true)->pluck('name', 'id'))
                            ->searchable(),
                        TextInput::make('rating')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(5)
                            ->step(0.1),
                        Textarea::make('brief_description')
                            ->label('Brief Description')
                            ->helperText('A short description used for recommendations')
                            ->required()
                            ->columnSpanFull(),
                        Textarea::make('description')
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->collapsible(),

                // Visibility Settings Section
                Forms\Components\Section::make('Visibility Settings')
                    ->description('Control the visibility and featured status of this place')
                    ->schema([
                        Checkbox::make('is_active')
                            ->label('Active')
                            ->helperText('Toggle to show or hide this place from users')
                            ->default(true),
                        Checkbox::make('is_choice')
                            ->label('Rydo Choice')
                            ->helperText('Featured in Rydo Choices section on home page')
                            ->hiddenOn('edit')
                            ->default(true),
                        Checkbox::make('is_trending')
                            ->label('Trending')
                            ->helperText('Featured in Trending section on home page')
                            ->default(false),
                    ])
                    ->columns(3)
                    ->collapsible(),

                // Media Section
                Forms\Components\Section::make('Media')
                    ->description('Upload and manage place photos')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('place_photos')
                            ->collection('place_photos')
                            ->downloadable()
                            ->deletable()
                            ->multiple()
                            ->label('Place Photos')
                            ->image() // Only allow image files
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'image/gif']) // Specify allowed image types
                            ->required()
                            ->helperText('At least one image is required')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('place_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->limit(30)
                    ->searchable(),
                Tables\Columns\TextColumn::make('category.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('agent.name')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('favorites_count')
                    ->label('Favorites')
                    ->getStateUsing(fn ($record) => $record->favorites()->count())
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])->defaultSort('created_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->relationship('category', 'name'),
                Tables\Filters\SelectFilter::make('agent')
                    ->relationship('agent', 'name'),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),
                Tables\Filters\SelectFilter::make('has_favorites')
                    ->label('Favorites')
                    ->options([
                        'with_favorites' => 'Has favorites',
                        'without_favorites' => 'No favorites',
                        'most_favorites' => 'Most favorites (10+)',
                    ])
                    ->query(function ($query, $state) {
                        if ($state === 'with_favorites') {
                            return $query->whereHas('favorites');
                        }
                        if ($state === 'without_favorites') {
                            return $query->whereDoesntHave('favorites');
                        }
                        if ($state === 'most_favorites') {
                            return $query->withCount('favorites')
                                ->having('favorites_count', '>=', 10);
                        }
                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('removeFromChoices')
                    ->label('Remove from Choices')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->action(function (Place $record): void {
                        $record->update(['is_choice' => false]);
                    })
                    ->after(function () {
                        // Refresh the page to remove the record from the list
                        return redirect(ChoicePlaceResource::getUrl());
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('removeFromChoices')
                        ->label('Remove from Rydo Choices')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records): void {
                            $records->each(function ($record): void {
                                $record->update(['is_choice' => false]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChoicePlaces::route('/'),
            'create' => Pages\CreateChoicePlace::route('/create'),
            'edit' => Pages\EditChoicePlace::route('/{record}/edit'),
            'view' => Pages\ViewChoicePlace::route('/{record}'),
        ];
    }
}
