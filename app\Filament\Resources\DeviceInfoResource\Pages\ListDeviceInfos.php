<?php

namespace App\Filament\Resources\DeviceInfoResource\Pages;

use App\Filament\Resources\DeviceInfoResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDeviceInfos extends ListRecords
{
    protected static string $resource = DeviceInfoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
