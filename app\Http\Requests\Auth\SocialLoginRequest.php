<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class SocialLoginRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'provider' => ['required', 'string', 'in:google,apple'],
            'token' => ['required', 'string'],
            'device_id' => ['required', 'string', 'max:255'],
            'device_type' => ['required', 'string', 'in:android,ios,web,desktop'],
            'device_model' => ['nullable', 'string', 'max:255'],
            'device_brand' => ['nullable', 'string', 'max:255'],
            'app_version' => ['nullable', 'string', 'max:50'],
            'app_version_name' => ['nullable', 'string', 'max:50'],
            'language' => ['nullable', 'string', 'max:10'],
            'ip_address' => ['nullable', 'string', 'max:45'],
            'location' => ['nullable', 'string', 'max:255'],
            'os_version' => ['nullable', 'string', 'max:50'],
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        // If IP address is not provided, get it from the request
        if (empty($this->ip_address)) {
            $this->merge([
                'ip_address' => $this->ip(),
            ]);
        }

        // Convert device_type to lowercase
        if ($this->has('device_type')) {
            $this->merge([
                'device_type' => strtolower($this->device_type),
            ]);
        }
    }
}
