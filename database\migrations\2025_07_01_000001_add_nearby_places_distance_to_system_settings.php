<?php

use App\Models\SystemSetting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('system_settings')->updateOrInsert(
            ['key' => SystemSetting::KEY_NEARBY_PLACES_DISTANCE],
            [
                'key' => SystemSetting::KEY_NEARBY_PLACES_DISTANCE,
                'value' => '5',
                'description' => 'Default distance in kilometers for nearby places search',
                'type' => 'integer',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('system_settings')
            ->where('key', SystemSetting::KEY_NEARBY_PLACES_DISTANCE)
            ->delete();
    }
};
