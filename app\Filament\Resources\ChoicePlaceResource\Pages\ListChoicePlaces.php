<?php

namespace App\Filament\Resources\ChoicePlaceResource\Pages;

use App\Filament\Resources\ChoicePlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListChoicePlaces extends ListRecords
{
    protected static string $resource = ChoicePlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    $data['is_choice'] = true;
                    return $data;
                }),
        ];
    }
}
