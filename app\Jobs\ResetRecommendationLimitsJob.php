<?php

namespace App\Jobs;

use App\Services\TimeManipulationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class ResetRecommendationLimitsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Log the current time (with any offset applied)
        $now = TimeManipulationService::now();
        Log::info("Running ResetRecommendationLimitsJob at {$now} (with offset: " . TimeManipulationService::getTimeOffsetFormatted() . ")");

        Artisan::call('recommendations:reset-limits');
    }
}
