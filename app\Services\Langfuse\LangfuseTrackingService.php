<?php

namespace App\Services\Langfuse;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\User;
use App\Models\Agent;
use Illuminate\Support\Facades\Log;

class LangfuseTrackingService
{
    protected LangfuseService $langfuse;

    public function __construct(LangfuseService $langfuse)
    {
        $this->langfuse = $langfuse;
    }

    /**
     * Track a chat session in Langfuse
     */
    public function trackChatSession(ChatSession $chatSession): ?string
    {
        if (!$this->langfuse->isEnabled()) {
            return null;
        }

        try {
            $metadata = [
                'chat_session_id' => $chatSession->id,
                'agent_id' => $chatSession->agent_id,
                'agent_name' => $chatSession->agent->name ?? 'Unknown',
                'created_at' => $chatSession->created_at->toISOString(),
                'last_message_at' => $chatSession->last_message_at?->toISOString(),
            ];

            // Include user information if configured
            if (config('langfuse.sessions.include_user_info', true) && $chatSession->user) {
                $metadata['user_type'] = $chatSession->user->type?->value;
                $metadata['user_created_at'] = $chatSession->user->created_at->toISOString();
            }

            $sessionData = [
                'id' => $this->generateSessionId($chatSession),
                'userId' => $chatSession->user_id ? (string) $chatSession->user_id : null,
                'metadata' => $metadata,
            ];

            return $this->langfuse->createSession($sessionData);
        } catch (\Exception $e) {
            Log::warning('Failed to track chat session in Langfuse', [
                'chat_session_id' => $chatSession->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Track a chat message interaction in Langfuse
     */
    public function trackChatMessage(
        ChatMessage $message,
        ?string $prompt = null,
        ?array $openaiResponse = null,
        ?string $sessionId = null
    ): ?string {
        if (!$this->langfuse->isEnabled()) {
            return null;
        }

        try {
            // Generate session ID if not provided
            if (!$sessionId) {
                $sessionId = $this->generateSessionId($message->chatSession);
            }

            // Create trace for this message interaction
            $traceId = $this->createMessageTrace($message, $sessionId);
            
            if (!$traceId) {
                return null;
            }

            // If this is an agent message with OpenAI response, track the generation
            if ($message->sender->value === 'agent' && $openaiResponse && $prompt) {
                $this->trackOpenAIGeneration($traceId, $message, $prompt, $openaiResponse);
            }

            // Track the message as a span
            $this->trackMessageSpan($traceId, $message);

            return $traceId;
        } catch (\Exception $e) {
            Log::warning('Failed to track chat message in Langfuse', [
                'chat_message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Track OpenAI generation within a trace
     */
    public function trackOpenAIGeneration(
        string $traceId,
        ChatMessage $message,
        string $prompt,
        array $openaiResponse
    ): ?string {
        try {
            $usage = $openaiResponse['usage'] ?? [];
            $model = $openaiResponse['model'] ?? 'unknown';
            $output = $openaiResponse['choices'][0]['message']['content'] ?? $message->message_text;

            $generationData = [
                'traceId' => $traceId,
                'name' => 'OpenAI Chat Completion',
                'model' => $model,
                'input' => $prompt,
                'output' => $output,
                'usage' => [
                    'promptTokens' => $usage['prompt_tokens'] ?? null,
                    'completionTokens' => $usage['completion_tokens'] ?? null,
                    'totalTokens' => $usage['total_tokens'] ?? null,
                ],
                'metadata' => [
                    'chat_message_id' => $message->id,
                    'agent_id' => $message->agent_id,
                    'agent_name' => $message->agent->name ?? 'Unknown',
                    'is_transition' => $message->is_transition ?? false,
                    'is_recommended' => $message->is_recommended ?? false,
                    'openai_response_id' => $openaiResponse['id'] ?? null,
                ],
                'startTime' => $message->created_at->toISOString(),
                'endTime' => $message->created_at->toISOString(),
            ];

            return $this->langfuse->createGeneration($generationData);
        } catch (\Exception $e) {
            Log::warning('Failed to track OpenAI generation in Langfuse', [
                'trace_id' => $traceId,
                'chat_message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Track user feedback/reaction as a score
     */
    public function trackUserFeedback(
        string $traceId,
        string $feedbackType,
        $value,
        ?string $comment = null,
        ?string $observationId = null
    ): bool {
        try {
            $scoreData = [
                'traceId' => $traceId,
                'observationId' => $observationId,
                'name' => $feedbackType,
                'value' => $value,
                'comment' => $comment,
                'metadata' => [
                    'feedback_type' => $feedbackType,
                    'timestamp' => now()->toISOString(),
                ],
            ];

            return $this->langfuse->createScore($scoreData);
        } catch (\Exception $e) {
            Log::warning('Failed to track user feedback in Langfuse', [
                'trace_id' => $traceId,
                'feedback_type' => $feedbackType,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Create a trace for a message interaction
     */
    protected function createMessageTrace(ChatMessage $message, string $sessionId): ?string
    {
        $metadata = [
            'chat_message_id' => $message->id,
            'chat_session_id' => $message->chat_session_id,
            'sender' => $message->sender->value,
            'agent_id' => $message->agent_id,
            'agent_name' => $message->agent->name ?? 'Unknown',
            'is_transition' => $message->is_transition ?? false,
            'is_recommended' => $message->is_recommended ?? false,
            'credit_cost' => $message->credit_cost,
            'points_used' => $message->points_used,
        ];

        // Include user information if available and configured
        if (config('langfuse.tracing.include_user_data', true) && $message->user) {
            $metadata['user_id'] = $message->user_id;
            $metadata['user_type'] = $message->user->type?->value;
        }

        $traceData = [
            'name' => $message->sender->value === 'user' ? 'User Message' : 'Agent Response',
            'userId' => $message->user_id ? (string) $message->user_id : null,
            'sessionId' => $sessionId,
            'metadata' => $metadata,
            'tags' => [
                'chat',
                $message->sender->value,
                $message->agent->name ?? 'unknown-agent',
            ],
            'timestamp' => $message->created_at->toISOString(),
        ];

        return $this->langfuse->createTrace($traceData);
    }

    /**
     * Track message content as a span
     */
    protected function trackMessageSpan(string $traceId, ChatMessage $message): ?string
    {
        $spanData = [
            'traceId' => $traceId,
            'name' => $message->sender->value === 'user' ? 'User Input' : 'Agent Output',
            'input' => $message->sender->value === 'user' ? $message->message_text : null,
            'output' => $message->sender->value === 'agent' ? $message->message_text : null,
            'metadata' => [
                'message_length' => strlen($message->message_text),
                'has_quick_replies' => !empty($message->quick_replies),
                'quick_replies_count' => count($message->quick_replies ?? []),
            ],
            'startTime' => $message->created_at->toISOString(),
            'endTime' => $message->created_at->toISOString(),
        ];

        return $this->langfuse->createSpan($spanData);
    }

    /**
     * Generate a consistent session ID for Langfuse
     */
    protected function generateSessionId(ChatSession $chatSession): string
    {
        return 'chat_session_' . $chatSession->id;
    }
}
