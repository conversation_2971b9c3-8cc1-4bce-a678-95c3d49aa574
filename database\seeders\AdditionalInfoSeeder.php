<?php

namespace Database\Seeders;

use App\Models\AdditionalInfo;
use Illuminate\Database\Seeder;

class AdditionalInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define common additional info tags
        $additionalInfoTags = [
            [
                'name' => 'Wheelchair Accessible',
                'is_active' => true,
            ],
            [
                'name' => 'Outdoor Seating',
                'is_active' => true,
            ],
            [
                'name' => 'Free WiFi',
                'is_active' => true,
            ],
            [
                'name' => 'Pet Friendly',
                'is_active' => true,
            ],
            [
                'name' => 'Parking Available',
                'is_active' => true,
            ],
            [
                'name' => 'Family Friendly',
                'is_active' => true,
            ],
            [
                'name' => 'Vegetarian Options',
                'is_active' => true,
            ],
            [
                'name' => 'Vegan Options',
                'is_active' => true,
            ],
            [
                'name' => 'Gluten-Free Options',
                'is_active' => true,
            ],
            [
                'name' => 'Halal Options',
                'is_active' => true,
            ],
        ];

        // Create the additional info tags
        foreach ($additionalInfoTags as $tag) {
            AdditionalInfo::updateOrCreate(
                ['name' => $tag['name']],
                [
                    'is_active' => $tag['is_active'],
                ]
            );
        }

        // Only output info if command is available (when run from console, not from migration)
        if (isset($this->command)) {
            $this->command->info('Additional info tags seeded successfully.');
        }
    }
}
