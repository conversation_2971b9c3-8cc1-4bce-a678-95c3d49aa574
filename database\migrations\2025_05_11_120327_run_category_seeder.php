<?php

use Database\Seeders\CategorySeeder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This will run the CategorySeeder to populate the categories table
     * with predefined categories linked to specific agents.
     */
    public function up(): void
    {
        // Create a new instance of the CategorySeeder
        $seeder = new CategorySeeder();

        // Run the seeder
        $seeder->run();
    }

    /**
     * Reverse the migrations.
     * We don't need to do anything here as the categories can be
     * recreated by running the migration again.
     */
    public function down(): void
    {
        // No action needed for rollback
    }
};
