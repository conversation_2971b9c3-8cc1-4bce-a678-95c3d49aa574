<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\SettingsResource;
use App\Models\SystemSetting;
use Illuminate\Http\Resources\Json\ResourceCollection;

class SettingsController extends Controller
{
    /**
     * Get all public settings
     *
     * @return ResourceCollection
     */
    public function index(): ResourceCollection
    {
        $settings = SystemSetting::where('is_public', true)
        ->where('type','editor')
        ->get();

        return SettingsResource::collection($settings);
    }

    /**
     * Get a specific setting by key
     *
     * @param string $key
     * @return \Illuminate\Http\Response
     */
    public function show(string $key)
    {
        $setting = SystemSetting::where('key', $key)
            ->where('is_public', true)
            ->firstOrFail();

        if ($setting->type === 'editor') {
            return response()->view('settings.show', [
                'title' => $setting->description ?: ucwords(str_replace('_', ' ', $setting->key)),
                'content' => $setting->value
            ]);
        }

        return SettingsResource::make($setting); 
    }
}
