<?php

namespace App\Filament\Resources\ChatSessionResource\Pages;

use App\Enums\ChatMessageSender;
use App\Filament\Resources\ChatSessionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Illuminate\Support\Str;

class ViewChatSession extends ViewRecord
{
    protected static string $resource = ChatSessionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Chat Session Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('user.name')
                                    ->label('User'),
                                TextEntry::make('agent.name')
                                    ->label('Agent'),
                                TextEntry::make('status')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'active' => 'success',
                                        'closed' => 'danger',
                                        default => 'warning',
                                    }),
                                TextEntry::make('session_token')
                                    ->label('Session Token')
                                    ->copyable(),
                            ]),
                    ]),

                Section::make('Messages')
                    ->schema([
                        TextEntry::make('messages_count')
                            ->label('Total Messages')
                            ->state(function ($record) {
                                return $record->messages()->count();
                            }),
                        TextEntry::make('user_messages_count')
                            ->label('User Messages')
                            ->state(function ($record) {
                                return $record->messages()->whereIn('sender', ['user', ChatMessageSender::USER->value])->count();
                            }),
                        TextEntry::make('agent_messages_count')
                            ->label('Agent Messages')
                            ->state(function ($record) {
                                return $record->messages()->whereIn('sender', ['agent', ChatMessageSender::AGENT->value])->count();
                            }),
                    ])
                    ->columns(3),

                Section::make('Recent Messages')
                    ->schema([
                        TextEntry::make('recent_messages')
                            ->label('')
                            ->state(function ($record) {
                                $messages = $record->messages()
                                    ->orderBy('created_at', 'desc')
                                    ->limit(5)
                                    ->get()
                                    ->map(function ($message) {
                                        $senderValue = is_string($message->sender) ? $message->sender : $message->sender->value;
                                        $sender = ucfirst($senderValue);
                                        $time = $message->created_at->format('M d, Y H:i');
                                        $text = Str::limit($message->message_text, 100);

                                        return "**{$sender}** ({$time}):\n{$text}";
                                    })
                                    ->join("\n\n");

                                return $messages ?: 'No messages in this chat session.';
                            })
                            ->markdown()
                            ->columnSpanFull(),
                    ]),

                Section::make('OpenAI API Usage')
                    ->schema([
                        TextEntry::make('total_api_cost')
                            ->label('Total API Cost')
                            ->prefix('$')
                            ->numeric(6)
                            ->state(function ($record) {
                                return $record->total_api_cost;
                            }),
                        TextEntry::make('total_api_tokens')
                            ->label('Total Tokens')
                            ->state(function ($record) {
                                return $record->total_api_tokens;
                            }),
                        TextEntry::make('api_usage_count')
                            ->label('API Calls')
                            ->state(function ($record) {
                                return $record->apiUsages()->count();
                            }),
                    ])
                    ->columns(3),

                Section::make('Timestamps')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                        TextEntry::make('last_message_at')
                            ->label('Last Message At')
                            ->dateTime(),
                    ])
                    ->columns(3),
            ]);
    }
}
