<?php

namespace Database\Factories;

use App\Models\SocialProvider;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SocialProvider>
 */
class SocialProviderFactory extends Factory
{
    protected $model = SocialProvider::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'provider_id' => $this->faker->uuid,
            'provider' => $this->faker->randomElement(['google', 'facebook', 'apple']),
            'email' => $this->faker->email,
            'last_token' => $this->faker->uuid,
        ];
    }
}
