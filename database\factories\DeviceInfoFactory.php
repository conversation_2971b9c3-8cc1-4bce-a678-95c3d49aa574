<?php

namespace Database\Factories;

use App\Models\DeviceInfo;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeviceInfo>
 */
class DeviceInfoFactory extends Factory
{
    protected $model = DeviceInfo::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'device_id' => $this->faker->uuid,
            'device_type' => $this->faker->randomElement(['ios', 'android', 'web']),
            'device_model' => $this->faker->randomElement(['iPhone 12', 'Samsung Galaxy S21', 'Chrome']),
            'device_brand' => $this->faker->randomElement(['Apple', 'Samsung', 'Google']),
            'app_version' => $this->faker->numerify('1.#.#'),
            'app_version_name' => $this->faker->numerify('v#.#.#'),
            'os_version' => $this->faker->numerify('#.#.#'),
            'language' => $this->faker->languageCode,
            'ip_address' => $this->faker->ipv4,
            'location' => $this->faker->city,
        ];
    }
}
