<?php

namespace App\Services;

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    /**
     * Get available subscription plans for a platform
     */
    public function getAvailablePlans(): ?Plan
    {
        // For now, we only have Rydo Plus plan
        return Plan::where('code', SubscriptionPlan::RYDO_PLUS->value)
                  ->where('is_active', true)
                  ->first();
    }

    /**
     * Check if user can subscribe
     */
    public function canUserSubscribe(User $user): bool
    {
        $currentSubscription = Subscription::getCurrentSubscription($user->id);
        return $currentSubscription === null;
    }

    /**
     * Check if user can reactivate subscription
     */
    public function canUserReactivate(User $user): bool
    {
        $currentSubscription = Subscription::getCurrentSubscription($user->id);
        if ($currentSubscription) {
            return false; // Already has active subscription
        }

        $lastSubscription = Subscription::getLastSubscription($user->id);
        return $lastSubscription && $lastSubscription->canReactivate();
    }

    /**
     * Process a successful purchase
     */
    public function processPurchase(
        User $user,
        string $platform,
        string $productId,
        string $transactionId,
        bool $isReactivation = false,
        string $receiptData = null
    ): Subscription {
        return DB::transaction(function () use ($user, $platform, $productId, $transactionId, $isReactivation, $receiptData) {
            // Get the plan
            $plan = $this->getAvailablePlans();
            if (!$plan) {
                throw new \Exception('Plan not found');
            }

            // Check for existing active subscription
            $existingSubscription = Subscription::getCurrentSubscription($user->id);
            if ($existingSubscription) {
                throw new \Exception('User already has an active subscription');
            }

            // Create new subscription
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'plan' => $plan->code,
                'points' => $plan->points,
                'status' => SubscriptionStatus::ACTIVE,
                'start_date' => now(),
                'end_date' => now()->addDays($plan->duration_days),
                'platform_type' => $platform,
                'product_id' => $productId,
                'transaction_id' => $transactionId,
                'auto_renewal' => true,
                'receipt_data' => $receiptData,
                'purchase_date' => now(),
                'original_transaction_id' => $transactionId, // For first purchase, same as transaction_id
            ]);

            // Update user's premium status immediately
            $user->update(['is_premium' => true]);

            // Log the purchase
            Log::info('Subscription created', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'platform' => $platform,
                'product_id' => $productId,
                'transaction_id' => $transactionId,
                'is_reactivation' => $isReactivation,
                'is_premium_updated' => true,
            ]);

            return $subscription;
        });
    }

    /**
     * Get user's subscription status
     */
    public function getUserSubscriptionStatus(User $user): array
    {
        $currentSubscription = Subscription::getCurrentSubscription($user->id);
        $lastSubscription = Subscription::getLastSubscription($user->id);

        return [
            'has_active_subscription' => $currentSubscription !== null,
            'current_subscription' => $currentSubscription,
            'last_subscription' => $lastSubscription,
            'can_reactivate' => $this->canUserReactivate($user),
        ];
    }

    /**
     * Check subscription eligibility
     */
    public function checkEligibility(User $user): array
    {
        $canSubscribe = $this->canUserSubscribe($user);
        $canReactivate = $this->canUserReactivate($user);
        $currentSubscription = Subscription::getCurrentSubscription($user->id);

        $eligibility = [
            'can_subscribe' => $canSubscribe,
            'can_reactivate' => $canReactivate,
            'has_active_subscription' => $currentSubscription !== null,
        ];

        if (!$canSubscribe && !$canReactivate) {
            if ($currentSubscription) {
                $eligibility['reason'] = 'User already has an active subscription';
            } else {
                $eligibility['reason'] = 'User is not eligible for subscription or reactivation';
            }
        }

        return $eligibility;
    }

    /**
     * Cancel user's active subscription
     */
    public function cancelSubscription(User $user): Subscription
    {
        return DB::transaction(function () use ($user) {
            // Get the current active subscription
            $subscription = Subscription::getCurrentSubscription($user->id);

            if (!$subscription) {
                throw new \Exception('No active subscription found to cancel');
            }

            if ($subscription->status === SubscriptionStatus::CANCELED) {
                throw new \Exception('Subscription is already cancelled');
            }

            // Update subscription with cancellation details
            $subscription->update([
                'status' => SubscriptionStatus::CANCELED,
                'cancellation_date' => now(),
                'auto_renewal' => false,
            ]);

            // Update user's premium status immediately
            $user->update(['is_premium' => false]);

            // Log the cancellation
            Log::info('Subscription cancelled', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'is_premium_updated' => false,
            ]);

            return $subscription->fresh();
        });
    }

    /**
     * Check if user can cancel their subscription
     */
    public function canUserCancelSubscription(User $user): bool
    {
        $currentSubscription = Subscription::getCurrentSubscription($user->id);

        return $currentSubscription &&
               $currentSubscription->status === SubscriptionStatus::ACTIVE;
    }

    /**
     * Reactivate user's subscription
     */
    public function reactivateSubscription(
        User $user,
        string $platform,
        string $productId,
        string $transactionId,
        string $receiptData = null
    ): Subscription {
        return DB::transaction(function () use ($user, $platform, $productId, $transactionId, $receiptData) {
            // Check if user can reactivate
            if (!$this->canUserReactivate($user)) {
                throw new \Exception('User is not eligible for subscription reactivation');
            }

            // Get the last subscription to determine the plan
            $lastSubscription = Subscription::getLastSubscription($user->id);
            if (!$lastSubscription) {
                throw new \Exception('No previous subscription found for reactivation');
            }

            // Validate that the last subscription is eligible for reactivation
            if (!$lastSubscription->canReactivate()) {
                throw new \Exception('Previous subscription is not eligible for reactivation');
            }

            // Use the existing processPurchase method with reactivation flag
            $newSubscription = $this->processPurchase(
                $user,
                $platform,
                $productId,
                $transactionId,
                true, // isReactivation = true
                $receiptData
            );

            // Log the reactivation
            Log::info('Subscription reactivated', [
                'user_id' => $user->id,
                'new_subscription_id' => $newSubscription->id,
                'previous_subscription_id' => $lastSubscription->id,
                'platform' => $platform,
                'product_id' => $productId,
                'transaction_id' => $transactionId,
            ]);

            return $newSubscription;
        });
    }

    /**
     * Get reactivation eligibility details
     */
    public function getReactivationEligibility(User $user): array
    {
        $canReactivate = $this->canUserReactivate($user);
        $currentSubscription = Subscription::getCurrentSubscription($user->id);
        $lastSubscription = Subscription::getLastSubscription($user->id);

        $eligibility = [
            'can_reactivate' => $canReactivate,
            'has_active_subscription' => $currentSubscription !== null,
        ];

        if ($canReactivate && $lastSubscription) {
            $eligibility['previous_subscription'] = [
                'plan' => $lastSubscription->plan->value,
                'status' => $lastSubscription->status->value,
                'ended_at' => $lastSubscription->end_date->toISOString(),
                'was_cancelled' => $lastSubscription->isCancelled(),
            ];
        }

        if (!$canReactivate) {
            if ($currentSubscription) {
                $eligibility['reason'] = 'User already has an active subscription';
            } elseif (!$lastSubscription) {
                $eligibility['reason'] = 'No previous subscription found';
            } elseif (!$lastSubscription->canReactivate()) {
                $eligibility['reason'] = 'Previous subscription is not eligible for reactivation';
            } else {
                $eligibility['reason'] = 'User is not eligible for reactivation';
            }
        }

        return $eligibility;
    }
}
