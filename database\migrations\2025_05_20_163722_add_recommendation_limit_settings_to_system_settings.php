<?php

use App\Models\SystemSetting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add recommendation limit settings to system_settings
        DB::table('system_settings')->insert([
            [
                'key' => SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                'value' => '10',
                'description' => 'Maximum number of recommendations allowed for registered users',
                'type' => 'integer',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
                'value' => '7',
                'description' => 'Number of days until a user\'s recommendation limit resets',
                'type' => 'integer',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the added settings
        DB::table('system_settings')
            ->whereIn('key', [
                SystemSetting::KEY_RECOMMENDATION_LIMIT_REGISTERED,
                SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
            ])
            ->delete();
    }
};
