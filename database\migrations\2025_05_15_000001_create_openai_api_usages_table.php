<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('openai_api_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('chat_message_id')->nullable()->constrained()->nullOnDelete();
            $table->string('model');
            $table->string('source');
            $table->string('action')->nullable();
            $table->string('request_type')->nullable();
            $table->string('endpoint')->nullable();
            $table->integer('prompt_tokens')->default(0);
            $table->integer('completion_tokens')->default(0);
            $table->integer('total_tokens')->default(0);
            $table->decimal('prompt_cost', 10, 6)->default(0);
            $table->decimal('completion_cost', 10, 6)->default(0);
            $table->decimal('total_cost', 10, 6)->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Indexes for faster queries
            $table->index(['user_id', 'created_at']);
            $table->index(['model', 'created_at']);
            $table->index(['source', 'created_at']);
            $table->index(['chat_message_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('openai_api_usages');
    }
};
