<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Subcategory;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Drop the subcategories table
     */
    public function up(): void
    {
        Schema::dropIfExists('subcategories');
    }

    /**
     * Reverse the migrations.
     * Recreate the subcategories table
     */
    public function down(): void
    {
        Schema::create('subcategories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }
};
