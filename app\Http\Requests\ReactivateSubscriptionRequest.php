<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReactivateSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by auth:sanctum middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'platform' => 'required|string|in:ios,android',
            'product_id' => 'required|string',
            'receipt_data' => 'required|string',
            'transaction_id' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'platform.required' => 'Platform is required.',
            'platform.in' => 'Platform must be either ios or android.',
            'product_id.required' => 'Product ID is required.',
            'receipt_data.required' => 'Receipt data is required.',
            'transaction_id.required' => 'Transaction ID is required.',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'platform' => 'platform',
            'product_id' => 'product ID',
            'receipt_data' => 'receipt data',
            'transaction_id' => 'transaction ID',
        ];
    }
}
