<?php

namespace App\Filament\Resources;

use App\Filament\Actions\IsAdminAction;
use App\Filament\Resources\OpenAIApiUsageResource\Pages;
use App\Models\OpenAIApiUsage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Indicator;
use Illuminate\Database\Eloquent\Model;

class OpenAIApiUsageResource extends Resource
{
    protected static ?string $model = OpenAIApiUsage::class;

    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';

    protected static ?string $navigationGroup = 'AI Management';

    protected static ?string $navigationLabel = 'OpenAI API Usage';

    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('model')
                            ->required()
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('source')
                            ->required()
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('action')
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('request_type')
                            ->maxLength(255)
                            ->disabled(),
                        Forms\Components\TextInput::make('endpoint')
                            ->maxLength(255)
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Token Usage')
                    ->schema([
                        Forms\Components\TextInput::make('prompt_tokens')
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('completion_tokens')
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('total_tokens')
                            ->numeric()
                            ->disabled(),
                    ])->columns(3),

                Forms\Components\Section::make('Cost Information')
                    ->schema([
                        Forms\Components\TextInput::make('prompt_cost')
                            ->numeric(6)
                            ->prefix('$')
                            ->disabled(),
                        Forms\Components\TextInput::make('completion_cost')
                            ->numeric(6)
                            ->prefix('$')
                            ->disabled(),
                        Forms\Components\TextInput::make('total_cost')
                            ->numeric(6)
                            ->prefix('$')
                            ->disabled(),
                    ])->columns(3),

                Forms\Components\Section::make('Relationships')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->disabled(),
                        Forms\Components\Select::make('chat_message_id')
                            ->relationship('chatMessage', 'id')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Metadata')
                    ->schema([
                        Forms\Components\Repeater::make('metadata')
                            ->schema([
                                Forms\Components\TextInput::make('key')
                                    ->disabled(),
                                Forms\Components\TextInput::make('value')
                                    ->disabled(),
                            ])
                            ->columns(2)
                            ->itemLabel(fn (array $state): ?string => $state['key'] ?? null)
                            ->disabled()
                            ->afterStateHydrated(function (Forms\Components\Repeater $component, $state) {
                                if (!is_array($state)) {
                                    return;
                                }

                                $items = [];
                                foreach ($state as $key => $value) {
                                    $items[] = [
                                        'key' => $key,
                                        'value' => is_array($value) || is_object($value)
                                            ? json_encode($value, JSON_PRETTY_PRINT)
                                            : (string) $value,
                                    ];
                                }

                                $component->state($items);
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('model')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('source')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('action')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_tokens')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_cost')
                    ->prefix('$')
                    ->numeric(6)
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('model')
                    ->options(function () {
                        return OpenAIApiUsage::distinct()->pluck('model', 'model')->toArray();
                    }),
                SelectFilter::make('source')
                    ->options(function () {
                        return OpenAIApiUsage::distinct()->pluck('source', 'source')->toArray();
                    }),
                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['created_from'] ?? null) {
                            $indicators[] = Indicator::make('Created from ' . $data['created_from'])
                                ->removeField('created_from');
                        }

                        if ($data['created_until'] ?? null) {
                            $indicators[] = Indicator::make('Created until ' . $data['created_until'])
                                ->removeField('created_until');
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // No bulk actions needed for this resource
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOpenAIApiUsages::route('/'),
            'view' => Pages\ViewOpenAIApiUsage::route('/{record}'),
        ];
    }

    public static function canViewAny(): bool
    {
        return IsAdminAction::handle();
    }
}
