<?php

namespace App\Actions\OpenAI;

use App\Enums\ModelType;
use App\Models\OpenAIApiUsage;
use App\Services\OpenAI\OpenAITrackingService;
use Illuminate\Support\Facades\Log;

class TrackOpenAIUsageAction
{
    protected $trackingService;

    public function __construct(OpenAITrackingService $trackingService)
    {
        $this->trackingService = $trackingService;
    }

    /**
     * Execute the action to track OpenAI API usage
     *
     * @param string|ModelType $model The model used
     * @param array $response The response from OpenAI API
     * @param array $metadata Additional metadata
     * @param int|null $userId User ID if available
     * @param int|null $chatMessageId Chat message ID if available
     * @param string $source Source of the API call
     * @param string|null $action Specific action being performed
     * @param int|null $chatSessionId Chat session ID if available
     * @return OpenAIApiUsage
     */
    public function execute(
        string|ModelType $model,
        array $response,
        array $metadata = [],
        ?int $userId = null,
        ?int $chatMessageId = null,
        string $source = 'unknown',
        ?string $action = null,
        ?int $chatSessionId = null
    ): OpenAIApiUsage {
        // Convert ModelType enum to string if needed
        $modelString = $model instanceof ModelType ? $model->value : $model;
        // Extract token usage from response
        $tokenUsage = $this->extractTokenUsage($response);

        Log::info('Starting OpenAI API usage tracking', [
            'model' => $modelString,
            'has_chat_message' => $chatMessageId ? 'yes' : 'no',
            'chat_message_id' => $chatMessageId,
            'request_type' => $metadata['request_type'] ?? 'unknown',
            'endpoint' => $metadata['endpoint'] ?? 'unknown',
            'metadata' => $metadata,
        ]);

        Log::info('Extracted token usage', $tokenUsage);

        // Extract chat_session_id from metadata if available
        if (!$chatSessionId && isset($metadata['chat_session_id'])) {
            $chatSessionId = $metadata['chat_session_id'];
        }

        // If we have a chat_message_id but no chat_session_id, try to get it from the message
        if (!$chatSessionId && $chatMessageId) {
            try {
                $chatMessage = \App\Models\ChatMessage::find($chatMessageId);
                if ($chatMessage) {
                    $chatSessionId = $chatMessage->chat_session_id;
                }
            } catch (\Exception $e) {
                Log::warning('Failed to get chat_session_id from chat_message', [
                    'chat_message_id' => $chatMessageId,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Track usage
        $usage = $this->trackingService->trackUsage(
            $modelString,
            $tokenUsage['prompt_tokens'],
            $tokenUsage['completion_tokens'],
            $metadata,
            $userId,
            $chatMessageId,
            $source,
            $action,
            $chatSessionId
        );

        Log::info('Successfully tracked OpenAI API usage', [
            'model' => $modelString,
            'source' => $source,
        ]);

        return $usage;
    }

    /**
     * Extract token usage from OpenAI API response
     *
     * @param array $response
     * @return array
     */
    private function extractTokenUsage(array $response): array
    {
        // Default values
        $promptTokens = 0;
        $completionTokens = 0;
        $totalTokens = 0;

        // Try to extract token usage from response
        if (isset($response['usage'])) {
            $promptTokens = $response['usage']['prompt_tokens'] ?? 0;
            $completionTokens = $response['usage']['completion_tokens'] ?? 0;
            $totalTokens = $response['usage']['total_tokens'] ?? 0;
        }

        return [
            'prompt_tokens' => $promptTokens,
            'completion_tokens' => $completionTokens,
            'total_tokens' => $totalTokens,
        ];
    }
}
