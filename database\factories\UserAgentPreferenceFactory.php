<?php

namespace Database\Factories;

use App\Models\Agent;
use App\Models\User;
use App\Models\UserAgentPreference;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserAgentPreference>
 */
class UserAgentPreferenceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserAgentPreference::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'agent_id' => Agent::factory(),
            'interest_level' => $this->faker->numberBetween(1, 5),
        ];
    }
}
