<?php

namespace App\Filament\Resources\PromptResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;

class AgentsRelationManager extends RelationManager
{
    protected static string $relationship = 'agents';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $title = 'Agents';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->description('Set the agent\'s name, title, and description')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Agent Name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('title')
                            ->label('Agent Title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('Agent Description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('Status')
                    ->description('Control the visibility and type of this agent')
                    ->schema([
                        Forms\Components\Toggle::make('is_visible')
                            ->label('Visible to Users')
                            ->helperText('Toggle to show or hide this agent from users')
                            ->default(true),
                        Forms\Components\Toggle::make('is_general')
                            ->label('General Agent')
                            ->helperText('If enabled, this agent will be used as a general agent')
                            ->default(false),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('Access Settings')
                    ->description('Configure points requirements for this agent')
                    ->schema([
                        Forms\Components\TextInput::make('points_required')
                            ->label('Points Required')
                            ->numeric()
                            ->default(100)
                            ->minValue(0)
                            ->maxValue(10000)
                            ->required()
                            ->helperText('Number of points required for a user to chat with this agent'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('avatar')
                    ->collection('agent_avatar')
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_visible')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_general')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('points_required')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_visible')
                    ->options([
                        '1' => 'Visible',
                        '0' => 'Hidden',
                    ])
                    ->label('Visibility'),
                Tables\Filters\SelectFilter::make('is_general')
                    ->options([
                        '1' => 'General',
                        '0' => 'Specific',
                    ])
                    ->label('Agent Type'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('toggleVisibility')
                        ->label('Toggle Visibility')
                        ->icon('heroicon-o-eye')
                        ->action(function (Tables\Actions\BulkAction $action, array $data) {
                            $status = $data['status'] ?? true;
                            
                            $action->getRecords()->each(function ($record) use ($status) {
                                $record->update(['is_visible' => $status]);
                            });
                            
                            \Filament\Notifications\Notification::make()
                                ->title('Visibility Updated')
                                ->body('The visibility has been updated for the selected agents.')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Toggle::make('status')
                                ->label('Visible')
                                ->default(true),
                        ]),
                ]),
            ]);
    }
}
