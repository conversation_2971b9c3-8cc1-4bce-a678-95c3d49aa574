<?php

namespace App\Filament\Resources\PointAdjustmentResource\Pages;

use App\Filament\Resources\PointAdjustmentResource;
use Filament\Resources\Pages\EditRecord;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class EditPointAdjustment extends EditRecord
{
    protected static string $resource = PointAdjustmentResource::class;

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['admin_id'] = Auth::id();
        $user = User::find($data['user_id']);
        $originalAdjustment = $this->record;

        // Revert the original adjustment
        switch ($originalAdjustment->type) {
            case 'add':
                $user->points -= $originalAdjustment->amount;
                break;
            case 'deduct':
                $user->points += $originalAdjustment->amount;
                break;
            case 'reset':
                // We can't revert a reset, so we'll just apply the new adjustment
                break;
        }

        // Apply the new adjustment
        switch ($data['type']) {
            case 'add':
                $user->points += $data['amount'];
                break;
            case 'deduct':
                $user->points = max(0, $user->points - $data['amount']);
                break;
            case 'reset':
                $user->points = $data['amount'];
                break;
        }

        $user->save();

        return $data;
    }
}
