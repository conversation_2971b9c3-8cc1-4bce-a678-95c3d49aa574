<?php

namespace App\Listeners;

use App\Events\UserDeleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class HandleUserDeletion implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserDeleted $event): void
    {
        $user = $event->user;
        $newEmail = $event->newEmail;

        DB::transaction(function () use ($user, $newEmail) {
            // Update email to prevent conflicts with future registrations
            $user->email = $newEmail;
            $user->save();

            // Soft delete related records
            $user->socialProviders()->delete();
            $user->agentPreferences()->delete();
            $user->chatSessions()->delete();
            $user->messageReactions()->delete();
            $user->messageReports()->delete();
            $user->subscriptions()->delete();
            $user->paymentHistory()->delete();
            $user->recommendations()->delete();
            $user->featureUsageStatistics()->delete();
            $user->deviceInfo()->delete();
            $user->placeFavorites()->delete();
            $user->recommendationUsage()->delete();

            // Finally soft delete the user
            $user->delete();
        });
    }
}
