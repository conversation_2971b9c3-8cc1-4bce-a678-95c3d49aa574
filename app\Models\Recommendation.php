<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Recommendation extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'credit_cost' => 'decimal:2',
        'places_ids' => 'array',
        'deleted_at' => 'datetime',
    ];

    protected $with = ['agent'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @deprecated Use places() instead
     */
    public function place(): BelongsTo
    {
        return $this->belongsTo(Place::class, 'old_place_id');
    }

    /**
     * Get the places associated with this recommendation.
     * This uses the places_ids JSON column to retrieve multiple places.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function places()
    {
        if (!$this->places_ids) {
            return collect();
        }

        return Place::whereIn('id', $this->places_ids)->get();
    }

    /**
     * Format the places as a text string for display
     *
     * @return string
     */
    public function getFormattedPlacesAttribute(): string
    {
        $places = $this->places();

        if ($places->isEmpty()) {
            return '';
        }

        return $places->map(function ($place) {
            $name = is_array($place->name) ? ($place->name['en'] ?? '') : $place->name;
            return "{$name} – {$place->brief_description}";
        })->implode('  ');
    }

    /**
     * Get the place names as a comma-separated string for display in tables
     *
     * @return string
     */
    public function getPlaceNamesAttribute(): string
    {
        $places = $this->places();

        if ($places->isEmpty()) {
            return is_array($this->places_ids) ? implode(', ', $this->places_ids) : '';
        }

        return $places->map(function ($place) {
            return is_array($place->name) ? ($place->name['en'] ?? '') : $place->name;
        })->implode(', ');
    }

    public function chatMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }
}
