<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->string('platform_type')->nullable()->after('status'); // ios, android
            $table->string('product_id')->nullable()->after('platform_type'); // Store product ID
            $table->string('transaction_id')->nullable()->after('product_id'); // Platform transaction ID
            $table->boolean('auto_renewal')->default(true)->after('transaction_id'); // Auto renewal status
            $table->text('receipt_data')->nullable()->after('auto_renewal'); // Store receipt for validation
            $table->timestamp('purchase_date')->nullable()->after('receipt_data'); // When purchase was made
            $table->string('original_transaction_id')->nullable()->after('purchase_date'); // For renewals
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn([
                'platform_type',
                'product_id',
                'transaction_id',
                'auto_renewal',
                'receipt_data',
                'purchase_date',
                'original_transaction_id'
            ]);
        });
    }
};
