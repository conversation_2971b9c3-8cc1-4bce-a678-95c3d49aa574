# Helicone Integration for OpenAI Monitoring

This document explains how to use the Helicone integration in the Rydo backend for monitoring and analytics of OpenAI API usage.

## Overview

Helicone is a monitoring and analytics platform for OpenAI API usage. When enabled, all OpenAI API requests are proxied through Helicone, allowing you to:

- Monitor API usage and costs
- Track performance metrics
- Analyze request patterns
- Set up alerts and notifications
- View detailed analytics dashboards

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# Helicone Configuration (for OpenAI monitoring and analytics)
HELICONE_ENABLED=true
HELICONE_API_KEY=sk-helicone-tlzmwwi-3apexuim5s52q
HELICONE_BASE_URL=https://oai.helicone.ai/v1
```

### Configuration Options

- `HELICONE_ENABLED`: Set to `true` to enable Helicone monitoring, `false` to disable
- `HELICONE_API_KEY`: Your Helicone API key (required when enabled)
- `HELICONE_BASE_URL`: Helicone proxy URL (defaults to `https://oai.helicone.ai/v1`)

## How It Works

When Helicone is enabled:

1. The `OpenAIService` creates a custom OpenAI client instance
2. The client is configured to use Helicone's base URL instead of OpenAI's direct API
3. The Helicone authentication header is added to all requests
4. All OpenAI API calls are automatically routed through Helicone for monitoring

When Helicone is disabled:

1. The service uses the standard OpenAI API endpoints directly
2. No additional headers or proxy configuration is applied

## Implementation Details

### Service Modifications

The `OpenAIService` class has been modified to:

- Create a custom OpenAI client with conditional Helicone configuration
- Replace facade usage with the custom client instance
- Maintain all existing method signatures and functionality
- Preserve backward compatibility

### Key Changes

1. **Custom Client Creation**: The `createClient()` method configures the OpenAI client with optional Helicone settings
2. **Conditional Configuration**: Helicone is only applied when enabled and an API key is provided
3. **Header Authentication**: Uses the `Helicone-Auth` header with Bearer token authentication
4. **Base URL Override**: Routes requests through Helicone's proxy URL

## Usage

No code changes are required in your application. Simply:

1. Set the environment variables in your `.env` file
2. Restart your application
3. All OpenAI API calls will automatically be monitored through Helicone

## Testing

The integration includes comprehensive tests to ensure:

- Proper client creation with and without Helicone
- Graceful fallback when Helicone API key is missing
- Maintenance of the existing service interface
- Compatibility with existing functionality

Run the tests with:

```bash
php artisan test tests/Unit/Services/AI/OpenAIServiceTest.php
```

## Monitoring

Once enabled, you can monitor your OpenAI usage by:

1. Logging into your Helicone dashboard
2. Viewing real-time API usage statistics
3. Analyzing cost and performance metrics
4. Setting up alerts for usage thresholds

## Troubleshooting

### Common Issues

1. **Invalid API Key**: Ensure your `HELICONE_API_KEY` is correct
2. **Network Issues**: Verify connectivity to `oai.helicone.ai`
3. **Configuration**: Check that `HELICONE_ENABLED=true` in your environment

### Debugging

To debug Helicone integration:

1. Check Laravel logs for any HTTP client errors
2. Verify environment variables are loaded correctly
3. Test with Helicone disabled to isolate issues

## Security Considerations

- Store the Helicone API key securely in environment variables
- Never commit API keys to version control
- Use different API keys for different environments (development, staging, production)
- Regularly rotate API keys as per security best practices
