<?php

namespace App\Console\Commands;

use App\Models\SystemSetting;
use App\Models\User;
use App\Services\TimeManipulationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RenewGuestPointsCommand extends Command
{
    protected $signature = 'points:renew-guest';
    protected $description = 'Renew points for guest users based on renewal interval';

    public function handle()
    {
        $now = TimeManipulationService::now();
        Log::info("Running RenewGuestPointsJob at {$now} (with offset: " . TimeManipulationService::getTimeOffsetFormatted() . ")");

        $renewalDays = SystemSetting::getValue(SystemSetting::KEY_GUEST_POINTS_RENEWAL_DAYS, 7);
        $renewalAmount = SystemSetting::getValue(SystemSetting::KEY_GUEST_POINTS_RENEWAL_AMOUNT, 100);

        $users = User::query()
            ->where('is_anonymous', true)
            ->where(function ($query) use ($now) {
                $query->whereNull('next_points_renewal_at')
                    ->orWhere('next_points_renewal_at', '<=', $now);
            })
            ->get();

        $count = 0;
        foreach ($users as $user) {
            try {
                $user->resetSomePoints(
                    $renewalAmount,
                    'Periodic points renewal for guest user'
                );

                $user->last_points_renewal_at = $now;
                $user->next_points_renewal_at = $now->addDays($renewalDays);
                $user->save();

                $count++;
            } catch (\Exception $e) {
                Log::error("Failed to renew points for user {$user->id}: " . $e->getMessage());
            }
        }

        $this->info("Successfully renewed points for {$count} guest users");
        return \Symfony\Component\Console\Command\Command::SUCCESS;
    }
}
