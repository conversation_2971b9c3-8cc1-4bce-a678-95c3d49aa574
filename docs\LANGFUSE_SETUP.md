# Langfuse Setup Guide

## Quick Setup Instructions

### 1. Install Dependencies
The integration uses Guzzle HTTP client which is already included in Laravel, so no additional composer packages are needed.

### 2. Add Environment Variables
Add these variables to your `.env` file:

```env
# Langfuse Configuration
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=pk_lf_your_public_key_here
LANGFUSE_SECRET_KEY=sk_lf_your_secret_key_here
LANGFUSE_HOST=https://cloud.langfuse.com

# Optional Configuration (with defaults)
LANGFUSE_TIMEOUT=30
LANGFUSE_CONNECT_TIMEOUT=10
LANGFUSE_INCLUDE_USER_DATA=true
LANGFUSE_INCLUDE_METADATA=true
LANGFUSE_TRACE_IN_DEVELOPMENT=true
LANGFUSE_TRACE_IN_TESTING=false
LANGFUSE_MAX_CONTENT_LENGTH=10000
LANGFUSE_AUTO_CREATE_SESSIONS=true
LANGFUSE_INCLUDE_AGENT_INFO=true
LANGFUSE_INCLUDE_USER_INFO=true
LANGFUSE_FAIL_SILENTLY=true
LANGFUSE_LOG_ERRORS=true
LANGFUSE_LOG_LEVEL=warning
```

### 3. Get Langfuse Credentials

#### Option A: Langfuse Cloud (Recommended)
1. Go to [https://cloud.langfuse.com](https://cloud.langfuse.com)
2. Sign up for a free account
3. Create a new project
4. Go to Settings → API Keys
5. Copy the Public Key and Secret Key
6. Add them to your `.env` file

#### Option B: Self-Hosted Langfuse
1. Follow the [Langfuse self-hosting guide](https://langfuse.com/docs/deployment/self-host)
2. Set `LANGFUSE_HOST` to your instance URL
3. Generate API keys in your instance admin panel

### 4. Clear Configuration Cache
```bash
php artisan config:clear
php artisan config:cache
```

### 5. Test the Integration
Start using your chat functionality normally. The integration will automatically:
- Track all OpenAI API calls
- Create Langfuse sessions for chat conversations
- Log traces for each message interaction
- Monitor costs and token usage

### 6. View Analytics
1. Log into your Langfuse dashboard
2. Navigate to your project
3. View traces, sessions, and analytics

## Verification

To verify the integration is working:

1. **Check Logs**: Look for Langfuse-related log entries:
   ```bash
   php artisan pail --filter=langfuse
   ```

2. **Send Test Messages**: Create a chat session and send messages through your app

3. **Check Langfuse Dashboard**: Within a few seconds, you should see:
   - New traces appearing
   - Session data
   - Token usage and costs

## Troubleshooting

### Common Issues

**Integration not working:**
- Verify `LANGFUSE_ENABLED=true`
- Check API keys are correct (they should start with `pk_lf_` and `sk_lf_`)
- Ensure no typos in environment variable names

**No data appearing:**
- Check that chat messages are being created properly
- Verify OpenAI integration is working
- Look for error logs

**Performance issues:**
- Set `LANGFUSE_FAIL_SILENTLY=true` in production
- Reduce `LANGFUSE_MAX_CONTENT_LENGTH` if needed
- Consider enabling batching for high-volume apps

### Getting Help

- Check the [Langfuse documentation](https://langfuse.com/docs)
- Review Laravel logs for error messages
- Verify your Langfuse project settings

## What Gets Tracked

Once configured, the integration automatically tracks:

### Chat Sessions
- User information (if enabled)
- Agent information
- Session metadata
- Conversation duration

### Individual Messages
- User inputs
- Agent responses
- OpenAI API calls
- Token usage and costs
- Response times
- Message metadata

### OpenAI Generations
- Model used
- Prompt and response content
- Token counts (prompt, completion, total)
- API response metadata
- Cost calculations

## Privacy Considerations

- Set `LANGFUSE_INCLUDE_USER_DATA=false` if you want to exclude user information
- Configure `LANGFUSE_MAX_CONTENT_LENGTH` to limit content size
- Review what metadata is being sent to Langfuse
- Consider data retention policies in your Langfuse project settings

## Next Steps

After setup, explore these Langfuse features:

1. **Analytics Dashboard**: Monitor usage patterns and costs
2. **User Analytics**: Track per-user metrics
3. **Model Comparison**: Compare performance across different models
4. **Quality Monitoring**: Set up alerts for response quality
5. **Cost Tracking**: Monitor and optimize LLM costs

The integration is now complete and will automatically track all your chat interactions!
