<?php

namespace App\Console\Commands;

use App\Models\SystemSetting;
use App\Models\UserRecommendationUsage;
use App\Services\TimeManipulationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ResetRecommendationLimitsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recommendations:reset-limits';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset recommendation limits for users whose renewal period has expired';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $now = TimeManipulationService::now();
        Log::info("Running ResetRecommendationLimitsCommand at {$now} (with offset: " . TimeManipulationService::getTimeOffsetFormatted() . ")");

        // Get the renewal days from system settings
        $renewalDays = SystemSetting::getValue(
            SystemSetting::KEY_RECOMMENDATION_RENEWAL_DAYS,
            7
        );

        // Find all usage records where next_reset_at is in the past
        $expiredRecords = UserRecommendationUsage::query()
            ->whereNotNull('next_reset_at')
            ->where('next_reset_at', '<=', $now)
            ->get();

        $count = 0;
        foreach ($expiredRecords as $usage) {
            try {
                // Reset the count
                $usage->count = 0;
                $usage->last_reset_at = $now;
                $usage->next_reset_at = $now->copy()->addDays($renewalDays);

                // Reset notification status so user can be notified of this renewal
                $usage->has_been_notified_of_renewal = false;

                $usage->save();

                $count++;
            } catch (\Exception $e) {
                Log::error("Failed to reset recommendation limit for user {$usage->user_id}: " . $e->getMessage());
            }
        }

        $this->info("Successfully reset recommendation limits for {$count} users");
        Log::info("Successfully reset recommendation limits for {$count} users");

        return Command::SUCCESS;
    }
}
