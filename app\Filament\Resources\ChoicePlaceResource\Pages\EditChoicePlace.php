<?php

namespace App\Filament\Resources\ChoicePlaceResource\Pages;

use App\Filament\Resources\ChoicePlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditChoicePlace extends EditRecord
{
    protected static string $resource = ChoicePlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('removeFromChoices')
                ->label('Remove from Choices')
                ->color('danger')
                ->icon('heroicon-o-x-mark')
                ->action(function () {
                    $this->record->update(['is_choice' => false]);
                    $this->redirect(ChoicePlaceResource::getUrl());
                }),
        ];
    }
    
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['is_choice'] = true;
        
        return $data;
    }
}
