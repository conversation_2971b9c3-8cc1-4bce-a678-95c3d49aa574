<?php

namespace Tests\Feature\Auth;

use App\Enums\UserType;
use App\Models\DeviceInfo;
use App\Models\SocialProvider;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Socialite\Facades\Socialite;
use Laravel\Socialite\Two\User as SocialiteUser;
use Mockery;
use Tests\TestCase;

class SocialLoginTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'user_type' => UserType::Admin->value,
        ]);
        // Mock Socialite facade
        $this->mockSocialite();
    }

    /** @test */
    public function test_user_can_login_with_google()
    {
        // Arrange
        $socialUser = $this->createMockSocialiteUser();

        // Act
        $response = $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'test-device-123',
            'device_type' => 'android',
        ]);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'email',
                    'is_anonymous',
                    'avatar',
                ],
                'meta' => [
                    'token',
                ],
            ]);

        $this->assertDatabaseHas('users', [
            'email' => $socialUser->getEmail(),
            'is_anonymous' => false,
        ]);

        $this->assertDatabaseHas('social_providers', [
            'provider' => 'google',
            'provider_id' => $socialUser->getId(),
            'email' => $socialUser->getEmail(),
        ]);
    }

    /** @test */
    public function test_existing_user_can_login_with_social()
    {
        // Arrange
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'is_anonymous' => false,
            'user_type' => UserType::User->value,
        ]);

        $socialUser = $this->createMockSocialiteUser([
            'email' => '<EMAIL>',
        ]);

        // Act
        $response = $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'test-device-123',
            'device_type' => 'android',
        ]);

        // Assert
        $response->assertStatus(201);
        $this->assertDatabaseHas('social_providers', [
//            'user_id' => $user->id,
            'provider' => 'google',
            'provider_id' => $socialUser->getId(),
        ]);
    }

    /** @test */
    public function test_anonymous_user_can_convert_to_registered_with_social()
    {
        // Arrange
        $anonymousUser = User::factory()->create([
            'name' => 'Guest User',
            'email' => 'guest_' . Str::random(10) . '@example.com',
            'password' => Hash::make(Str::random(16)),
            'is_anonymous' => true,
            'is_first_login' => true,
        ]);

        $deviceInfo = DeviceInfo::create([
            'user_id' => $anonymousUser->id,
            'device_id' => 'test-device-123',
            'device_type' => 'android',
        ]);

        $socialUser = $this->createMockSocialiteUser();

        // Act
        $response = $this->postJson('/api/users/social-login', [
            'provider' => 'google',
            'token' => 'valid-token',
            'device_id' => 'test-device-123',
            'device_type' => 'android',
        ]);

        // Assert
        $response->assertStatus(201);

        $anonymousUser->refresh();
        $this->assertFalse($anonymousUser->is_anonymous);
        $this->assertEquals($socialUser->getEmail(), $anonymousUser->email);
        $this->assertEquals($socialUser->getName(), $anonymousUser->name);

        $this->assertDatabaseHas('social_providers', [
//            'user_id' => $anonymousUser->id,
            'provider' => 'google',
            'provider_id' => $socialUser->getId(),
        ]);
    }

    /** @test */
    public function test_validates_required_fields()
    {
        $response = $this->postJson('/api/users/social-login', [
            // Missing required fields
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['provider', 'token', 'device_id', 'device_type']);
    }

    /**
     * Mock the Socialite facade
     */
    /**
     * Fixed provider ID for consistent testing
     */
    protected $providerId = '12345';

    protected function mockSocialite()
    {
        $abstractUser = Mockery::mock(SocialiteUser::class);
        $abstractUser->shouldReceive('getId')
            ->andReturn($this->providerId)
            ->shouldReceive('getName')
            ->andReturn('John Doe')
            ->shouldReceive('getEmail')
            ->andReturn('<EMAIL>')
            ->shouldReceive('getAvatar')
            ->andReturn('https://example.com/avatar.jpg');

        $provider = Mockery::mock('Laravel\Socialite\Contracts\Provider');
        $provider->shouldReceive('userFromToken')
            ->andReturn($abstractUser);

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturn($provider);

        Socialite::shouldReceive('driver')
            ->with('apple')
            ->andReturn($provider);
    }

    /**
     * Create a mock Socialite user
     */
    protected function createMockSocialiteUser(array $overrides = [])
    {
        $user = Mockery::mock(SocialiteUser::class);
        $user->shouldReceive('getId')
            ->andReturn($overrides['id'] ?? $this->providerId);
        $user->shouldReceive('getName')
            ->andReturn($overrides['name'] ?? 'John Doe');
        $user->shouldReceive('getEmail')
            ->andReturn($overrides['email'] ?? '<EMAIL>');
        $user->shouldReceive('getAvatar')
            ->andReturn($overrides['avatar'] ?? 'https://example.com/avatar.jpg');

        return $user;
    }
}
