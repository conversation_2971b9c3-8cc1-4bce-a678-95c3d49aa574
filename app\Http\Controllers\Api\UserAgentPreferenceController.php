<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserAgentPreferenceBulkRequest;
use App\Http\Resources\UserAgentPreferenceResource;
use App\Models\UserAgentPreference;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserAgentPreferenceController extends Controller
{
    /**
     * Get all user agent preferences for the authenticated user.
     *
     * @param Request $request
     * @return ResourceCollection
     */
    public function index(Request $request): ResourceCollection
    {
        $preferences = UserAgentPreference::where('user_id', auth()->id())
            ->with('agent')
            ->get();

        return UserAgentPreferenceResource::collection($preferences);
    }

    /**
     * Store or update multiple user agent preferences.
     *
     * @param UserAgentPreferenceBulkRequest $request
     * @return JsonResponse
     */
    public function store(UserAgentPreferenceBulkRequest $request): JsonResponse
    {
            DB::beginTransaction();

            $interests = $request->input('interests');
            $userId = auth()->id();

            foreach ($interests as $interest) {
                UserAgentPreference::updateOrCreate(    
                    [
                        'user_id' => $userId,
                        'agent_id' => $interest['agent_id'],
                    ],
                    [
                        'interest_level' => $interest['interest_level'],
                    ]
                );
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Your interests have been updated!'
            ]);
    }
}
