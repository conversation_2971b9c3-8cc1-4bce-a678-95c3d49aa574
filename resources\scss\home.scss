// Color Variables
$white: #FFFFFF;
$black: #000000;

// Greyscale
$gray-700: #272835;  // Used in hero-content span
$gray-500: #666D80;  // Used in body color
$gray-900: #272835;

// Brand Colors
$primary-400: #5D4FBE;  // Primary/400
$light-bg: #F9F8FF;  // Used in body background

// Home Page Styles
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: $light-bg;
    color: $gray-500;
    line-height: 1.6;
}

.hero {
    min-height: 730px;
    display: flex;
    align-items: center;
    background-image: url('../../assets/images/landing/hero-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

    @media (max-width: 768px) {
        background-position: top left;
        min-height: auto;
        align-items: flex-start;
        padding: 120px 0;
        background-image: url('../../assets/images/landing/hero-mobile-bg.png');
    }

    .navbar {
        padding: 36px 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;

        .brand {
            img {
                height: 42px;
            }
        }
    }

    .hero-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 50%;

        @media (max-width: 768px) {
            width: 100%;
        }
        
        span {
            font-family: Poppins;
            font-weight: 700;
            font-size: 20px;
            line-height: 160%;
            letter-spacing: 0%;
            color: $gray-700;

            @media (max-width: 768px) {
                font-size: 16px;
            }
        }

        h1 {
            font-family: Poppins;
            font-weight: 400;
            font-size: 46px;
            line-height: 150%;
            letter-spacing: 0%;
            color: $gray-900;

            span {
                color: $primary-400;
                font-size: 46px;
                line-height: 100%;
                font-weight: 700;
            }

            @media (max-width: 768px) {
                font-size: 32px;

                span {
                    font-size: 32px;
                }
            }
        }

        .download-btns {
            display: flex;
            gap: 16px;

            a {
                text-decoration: none;

                img {
                    height: 55px;
                }
            }
        }
    }
}

.banner-section {
    margin: 50px 0;
    background-image: url('../../assets/images/landing/banner-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    padding: 52px;

    @media (max-width: 768px) {
        flex-direction: column;
        padding: 20px;
        background-image: url('../../assets/images/landing/banner-mobile-bg.png');
        align-items: flex-start;
        margin: 24px 0;
    }

    h2 {
        color: #fff;
        font-size: 32px;
        font-weight: 600;
        padding: 0;
        margin: 0;
        width: 35%;

        @media (max-width: 768px) {
            width: 100%;
            margin-bottom: 20px;
        }
    }

    p {
        color: #fff;
        font-size: 18px;
        font-weight: 400;
        padding: 0;
        margin: 0;
        width: 65%;

        @media (max-width: 768px) {
            width: 100%;
        }
    }
}

.agents-section {
    padding: 78px 0;

    .title {
        display: flex;
        flex-direction: column;
        text-align: center;
        gap: 6px;
        line-height: 1.5;
        width: 100%;
        margin: 0 auto;

        @media (max-width: 768px) {
            width: 100%;
        }

        span {
            color: $primary-400;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 0;

            @media (max-width: 768px) {
                font-size: 14px;
            }
        }

        h2 {
            font-weight: 400;
            color: $gray-900;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 0;

            @media (max-width: 768px) {
                font-size: 24px;
            }
        }

        p {
            font-size: 16px;
            font-weight: 400;
            color: $gray-500;
            margin-bottom: 0;

            @media (max-width: 768px) {
                font-size: 14px;
            }
        }
    }

    .agent-display {
        margin-top: 40px;
        background-image: url('../../assets/images/landing/agent-bg.png');
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        height: 700px;
        width: 100%;
        margin: 0 auto;
        position: relative;

        @media (max-width: 768px) {
            height: 300px;
            margin-top: 58px !important;
        }

        .item {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 10%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease-in-out;
            transform: translateX(50px);

            @media (max-width: 768px) {
                padding: 0;
                transform: translateX(0px);
            }

            &.active {
                opacity: 1;
                visibility: visible;
                transform: translateX(0);
            }
            
            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                z-index: 2;
            }

            h2 {
                font-size: 200px;
                color: #000;
                font-weight: 700;
                opacity: 0.1;
                position: absolute;
                bottom: 10%;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1;

                @media (max-width: 768px) {
                    font-size: 50px;
                }
            }

            .content {
                margin-top: 16px;
                display: flex;
                flex-direction: column;
                gap: 6px;
                text-align: center;
                z-index: 2;
                width: 50%;

                @media (max-width: 768px) {
                    width: 100%;
                }

                h3 {
                    font-size: 32px;
                    color: $gray-900;
                    font-weight: 700;

                    @media (max-width: 768px) {
                        font-size: 24px;
                    }
                }

                p {
                    color: $gray-500;
                    font-size: 18px;
                    font-weight: 400;
                    line-height: 1.5;

                    @media (max-width: 768px) {
                        font-size: 16px;
                        height: calc(24px * 3);
                    }
                }
            }
        }

        .agents-switcher {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 16px;
            padding-right: 20px;
            z-index: 3;

            .agent-switcher-btn {
                width: 56px;
                height: 56px;
                padding: 0;
                border: none;
                background: none;
                cursor: pointer;
                transition: transform 0.3s ease;
                opacity: 0.6;

                &:hover {
                    transform: scale(1.1);
                }

                &.active {
                    opacity: 1;
                    transform: scale(1.1);
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }
    }
}

.agents-section_v2 {
    padding: 78px 0;

    .title {
        display: flex;
        flex-direction: column;
        text-align: center;
        gap: 6px;
        line-height: 1.5;
        width: 100%;
        margin: 0 auto;
        margin-bottom: 48px;

        span {
            color: $primary-400;
            font-size: 16px;
            font-weight: 700;
        }

        h2 {
            font-size: 32px;
            color: $gray-900;
            font-weight: 600;
        }
    }
    
    .agents-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;

        @media (max-width: 768px) {
            grid-template-columns: repeat(1, 1fr);
        }

        .agent-card {
            display: flex;
            align-items: center;
            gap: 24px;
            flex-direction: column;

            .agent-img {
                width: 100%;

                img {
                    height: 360px;
                    border-radius: 16px;
                    width: 100%;
                    object-fit: cover;
                    object-position: center;

                    @media (max-width: 768px) {
                        height: 250px;
                    }
                }
            }

            .agent-content {
                h3 {
                    font-size: 24px;
                    font-weight: 600;
                    color: $gray-900;
                    margin-bottom: 8px;

                    @media (max-width: 768px) {
                        font-size: 20px;
                    }
                }

                p {
                    font-size: 16px;
                    font-weight: 400;
                    margin: 0;
                    padding: 0;
                    color: $gray-700;

                    @media (max-width: 768px) {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}

.why-rydo-section {
    padding-bottom: 100px;

    @media (max-width: 768px) {
        padding-bottom: 50px;
    }

    .title {
        display: flex;
        flex-direction: column;
        text-align: center;
        gap: 6px;
        line-height: 1.5;
        width: 50%;
        margin: 0 auto 60px;

        @media (max-width: 768px) {
            width: 100%;
        }

        span {
            color: $primary-400;
            font-size: 16px;
            font-weight: 700;
            margin: 0;
            padding: 0;

            @media (max-width: 768px) {
                font-size: 14px;
            }
        }
        
        h2 {
            font-size: 32px;
            font-weight: 600;
            color: $gray-900;
            margin: 0;
            padding: 0;

            @media (max-width: 768px) {
                font-size: 24px;
            }
        }
        
        p {
            font-size: 16px;
            font-weight: 400;
            color: $gray-500;
            margin: 0;
            padding: 0;

            @media (max-width: 768px) {
                font-size: 14px;
            }
        }
    }

    .cards-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        max-width: 1200px;
        margin: 0 auto;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }

        .card {
            background: $white;
            padding: 24px;
            border-radius: 24px;
            border: 1px solid rgba(208, 213, 221, 0.33);
            box-shadow: 0px 8px 27px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            flex-direction: row;
            gap: 24px;

            @media (max-width: 768px) {
                gap: 16px;
            }

            img {
                width: 72px;
                height: 72px;
                flex-shrink: 0;

                @media (max-width: 768px) {
                    height: 56px;
                    width: 56px;
                }
            }

            p {
                color: $gray-900;
                font-size: 20px;
                font-weight: 600;
                margin: 0;
                padding: 0;
                line-height: 1.4;
                text-align: left;

                @media (max-width: 768px) {
                    font-size: 16px;
                }
            }
        }
    }
}

.cta-box {
    background-image: url('../../assets/images/landing/cta-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 32px 64px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 64px;
    gap: 32px;

    @media (max-width: 768px) {
        flex-direction: column;
        padding: 20px;
        gap: 16px;
    }

    h2 {
        color: $gray-900;
        font-size: 28px;
        font-weight: 700;

        @media (max-width: 768px) {
            font-size: 22px;
        }
    }

    p {
        color: #252525;
        font-size: 20px;
        font-weight: 400;
        margin-bottom: 24px;
        padding: 0;

        @media (max-width: 768px) {
            font-size: 16px;
        }
    }

    .download-btns {
        display: flex;
        gap: 16px;

        a {
            text-decoration: none;

            img {
                height: 55px;

                @media (max-width: 768px) {
                    height: 40px;
                }
            }    
        }
    }
}

p.copyright {
    color: $gray-900;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    padding-bottom: 32px;
    margin: 0;
}