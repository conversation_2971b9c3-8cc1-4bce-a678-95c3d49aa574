<?php

namespace Tests\Unit\Services\AI;

use App\Services\AI\OpenAIService;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use OpenAI\Client;
use ReflectionClass;

class OpenAIServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * Test that OpenAI service creates client without Helicone when disabled
     */
    public function test_creates_client_without_helicone_when_disabled()
    {
        // Set configuration for test
        config([
            'openai.api_key' => 'test-api-key',
            'openai.organization' => 'test-org',
            'openai.helicone.enabled' => false,
        ]);

        $service = new OpenAIService();

        // Use reflection to access the protected client property
        $reflection = new ReflectionClass($service);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $client = $clientProperty->getValue($service);

        $this->assertInstanceOf(Client::class, $client);
    }

    /**
     * Test that OpenAI service creates client with Helicone when enabled
     */
    public function test_creates_client_with_helicone_when_enabled()
    {
        // Set configuration for test
        config([
            'openai.api_key' => 'test-api-key',
            'openai.organization' => 'test-org',
            'openai.helicone.enabled' => true,
            'openai.helicone.api_key' => 'test-helicone-key',
            'openai.helicone.base_url' => 'https://oai.helicone.ai/v1',
        ]);

        $service = new OpenAIService();

        // Use reflection to access the protected client property
        $reflection = new ReflectionClass($service);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $client = $clientProperty->getValue($service);

        $this->assertInstanceOf(Client::class, $client);
    }

    /**
     * Test that OpenAI service skips Helicone when enabled but no API key provided
     */
    public function test_skips_helicone_when_enabled_but_no_api_key()
    {
        // Set configuration for test
        config([
            'openai.api_key' => 'test-api-key',
            'openai.organization' => 'test-org',
            'openai.helicone.enabled' => true,
            'openai.helicone.api_key' => null, // No API key
            'openai.helicone.base_url' => 'https://oai.helicone.ai/v1',
        ]);

        $service = new OpenAIService();

        // Use reflection to access the protected client property
        $reflection = new ReflectionClass($service);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $client = $clientProperty->getValue($service);

        $this->assertInstanceOf(Client::class, $client);
    }

    /**
     * Test that service maintains the same interface
     */
    public function test_maintains_ai_service_interface()
    {
        config([
            'openai.api_key' => 'test-api-key',
        ]);

        $service = new OpenAIService();

        $this->assertTrue(method_exists($service, 'generateResponse'));
        $this->assertTrue(method_exists($service, 'streamResponse'));
    }

    /**
     * Test that service can be instantiated with custom model
     */
    public function test_can_instantiate_with_custom_model()
    {
        config([
            'openai.api_key' => 'test-api-key',
        ]);

        $service = new OpenAIService('gpt-3.5-turbo');

        // Use reflection to access the protected model property
        $reflection = new ReflectionClass($service);
        $modelProperty = $reflection->getProperty('model');
        $modelProperty->setAccessible(true);
        $model = $modelProperty->getValue($service);

        $this->assertEquals('gpt-3.5-turbo', $model);
    }
}
