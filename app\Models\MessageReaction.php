<?php

namespace App\Models;

use App\Enums\ReactionType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;

class MessageReaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'reaction' => ReactionType::class,
        'copied_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function chatMessage(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    protected function reactionLabel(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->reaction->label(),
        );
    }

    protected function reactionColor(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->reaction->color(),
        );
    }

    protected function reactionIcon(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->reaction->icon(),
        );
    }
}
