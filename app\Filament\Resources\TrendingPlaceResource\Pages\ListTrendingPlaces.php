<?php

namespace App\Filament\Resources\TrendingPlaceResource\Pages;

use App\Filament\Resources\TrendingPlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTrendingPlaces extends ListRecords
{
    protected static string $resource = TrendingPlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    $data['is_trending'] = true;
                    return $data;
                }),
        ];
    }
}
