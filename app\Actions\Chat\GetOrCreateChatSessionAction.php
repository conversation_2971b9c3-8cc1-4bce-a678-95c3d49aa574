<?php

namespace App\Actions\Chat;

use App\Models\Agent;
use App\Models\ChatSession;
use App\Http\Requests\MessageRequest;

class GetOrCreateChatSessionAction
{
    /**
     * Get existing or create a new chat session
     */
    public function execute(MessageRequest $request, $user, Agent $agent): ChatSession
    {
        return $request->chat_session_id
            ? ChatSession::query()
                ->where('id', $request->chat_session_id)
                ->where('user_id', $user->id)
                ->firstOrFail()
            : ChatSession::query()->create([
                'user_id' => $user->id,
                'agent_id' => $agent->id,
                'status' => 'active',
                'last_message_at' => now(),
            ]);
    }
}
