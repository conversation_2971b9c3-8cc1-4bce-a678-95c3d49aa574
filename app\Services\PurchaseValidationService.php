<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PurchaseValidationService
{
    /**
     * Validate iOS receipt
     */
    public function validateIosReceipt(string $receiptData, string $transactionId): bool
    {
        try {
            // For now, we'll implement basic validation
            // In production, you should validate with Apple's servers

            // Basic checks
            if (empty($receiptData) || empty($transactionId)) {
                return false;
            }

            // Decode base64 receipt data to check if it's valid
            $decodedReceipt = base64_decode($receiptData, true);
            if ($decodedReceipt === false) {
                Log::warning('Invalid base64 receipt data', [
                    'transaction_id' => $transactionId
                ]);
                return false;
            }

            // TODO: Implement actual Apple receipt validation
            // This would involve sending the receipt to Apple's verification servers
            // https://developer.apple.com/documentation/appstorereceipts/verifyreceipt

            Log::info('iOS receipt validation passed (basic)', [
                'transaction_id' => $transactionId
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('iOS receipt validation failed', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Validate Android receipt
     */
    public function validateAndroidReceipt(string $receiptData, string $transactionId): bool
    {
        try {
            // For now, we'll implement basic validation
            // In production, you should validate with Google Play's servers

            // Basic checks
            if (empty($receiptData) || empty($transactionId)) {
                return false;
            }

            // Try to decode JSON receipt data
            $receiptJson = json_decode($receiptData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::warning('Invalid JSON receipt data', [
                    'transaction_id' => $transactionId
                ]);
                return false;
            }

            // TODO: Implement actual Google Play receipt validation
            // This would involve using Google Play Developer API
            // https://developers.google.com/android-publisher/api-ref/rest/v3/purchases/products/get

            Log::info('Android receipt validation passed (basic)', [
                'transaction_id' => $transactionId
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Android receipt validation failed', [
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Validate receipt based on platform
     */
    public function validateReceipt(string $platform, string $receiptData, string $transactionId): bool
    {
        return match($platform) {
            'ios' => $this->validateIosReceipt($receiptData, $transactionId),
            'android' => $this->validateAndroidReceipt($receiptData, $transactionId),
            default => false
        };
    }

    /**
     * Check for duplicate transaction
     */
    public function isDuplicateTransaction(string $transactionId): bool
    {
        // Check if this transaction ID has already been processed
        // Look in subscriptions table for existing transaction_id
        $existingSubscription = \App\Models\Subscription::where('transaction_id', $transactionId)->first();

        if ($existingSubscription) {
            Log::warning('Duplicate transaction detected', [
                'transaction_id' => $transactionId,
                'existing_subscription_id' => $existingSubscription->id
            ]);
            return true;
        }

        // Also check payment_history table if it exists
        $existingPayment = \App\Models\PaymentHistory::where('transaction_id', $transactionId)->first();

        if ($existingPayment) {
            Log::warning('Duplicate transaction detected in payment history', [
                'transaction_id' => $transactionId,
                'existing_payment_id' => $existingPayment->id
            ]);
            return true;
        }

        return false;
    }
}
