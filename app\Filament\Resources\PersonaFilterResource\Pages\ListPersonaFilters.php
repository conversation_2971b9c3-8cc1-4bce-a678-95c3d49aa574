<?php

namespace App\Filament\Resources\PersonaFilterResource\Pages;

use App\Filament\Resources\PersonaFilterResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPersonaFilters extends ListRecords
{
    protected static string $resource = PersonaFilterResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
} 