<?php

namespace App\Filament\Pages;

use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\SystemSetting;
use App\Models\User;
use App\Services\SubscriptionPointsService;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class ChatSimulator extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-beaker';
    protected static ?string $navigationLabel = 'Chat Simulator';
    protected static ?string $title = 'Chat Points Simulator';
    protected static ?string $navigationGroup = 'Testing Tools';
    protected static ?int $navigationSort = 100;

    protected static string $view = 'filament.pages.chat-simulator';

    public ?array $data = [];
    public array $simulationLog = [];
    public ?int $selectedUserId = null;
    public ?int $selectedAgentId = null;
    public ?int $chatSessionId = null;
    public int $userPoints = 0;
    public int $agentPointsRequired = 0;
    public ?string $userRenewalInfo = null;
    public bool $isGuestUser = false;
    public ?int $dailyPointsLimit = null;
    public ?int $dailyPointsUsed = null;
    public bool $isUsingDowngradedPrompt = false;

    public function mount(): void
    {
        $this->form->fill();

        // Set the selected user ID if the authenticated user is a regular user
        $user = Auth::user();
        if ($user && $user->user_type === 'user') {
            $this->selectedUserId = $user->id;
            $this->updateUserPoints();
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Simulation Setup')
                    ->description('Select a user and agent to simulate chat interactions')
                    ->schema([
                        Select::make('user_id')
                            ->label('Select User')
                            ->options(User::where('user_type', 'user')
                                ->orderBy('name')
                                ->get()
                                ->mapWithKeys(function ($user) {
                                    $label = $user->name;
                                    if ($user->is_anonymous) {
                                        $label .= ' (Guest)';
                                    } elseif ($user->hasActiveSubscription()) {
                                        $label .= ' (Subscribed)';
                                    } else {
                                        $label .= ' (Registered)';
                                    }
                                    return [$user->id => $label];
                                }))
                            ->searchable()
                            ->required()
                            ->live()
                            ->default(function() {
                                $user = Auth::user();
                                return $user && $user->user_type === 'user' ? $user->id : null;
                            })
                            ->disabled(function() {
                                $user = Auth::user();
                                return $user && $user->user_type === 'user';
                            })
                            ->afterStateUpdated(function ($state) {
                                $this->selectedUserId = $state;
                                $this->updateUserPoints();
                            })
                            ->helperText('Select a user to simulate chat interactions'),

                        Select::make('agent_id')
                            ->label('Select Agent')
                            ->options(Agent::where('is_visible', true)->pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedAgentId = $state;
                                $this->updateAgentPointsRequired();
                            }),
                    ]),

                Section::make('Current Status')
                    ->schema([
                        Placeholder::make('user_points')
                            ->label('User Points')
                            ->content(function () {
                                return $this->userPoints;
                            }),

                        Placeholder::make('agent_points_required')
                            ->label('Agent Points Required')
                            ->content(function () {
                                return $this->agentPointsRequired;
                            }),

                        Placeholder::make('chat_session')
                            ->label('Active Chat Session')
                            ->content(function () {
                                return $this->chatSessionId ? "Session #{$this->chatSessionId}" : 'No active session';
                            }),

                        Placeholder::make('user_renewal_info')
                            ->label('Points Renewal Info')
                            ->content(function () {
                                return $this->userRenewalInfo ?? 'No renewal information';
                            }),

                        Placeholder::make('downgrade_prompt_status')
                            ->label('Downgrade Prompt Status')
                            ->content(function () {
                                if ($this->isUsingDowngradedPrompt) {
                                    return 'Using downgraded prompt (daily limit exceeded)';
                                } elseif ($this->dailyPointsLimit !== null) {
                                    return "Normal prompt ({$this->dailyPointsUsed}/{$this->dailyPointsLimit} daily points used)";
                                } else {
                                    return 'Not applicable';
                                }
                            })
                            ->visible(fn() => $this->selectedUserId !== null),
                    ]),

                Section::make('Actions')
                    ->schema([
                        Group::make([
                            \Filament\Forms\Components\Actions::make([
                                Action::make('create_session')
                                    ->label('Create New Chat Session')
                                    ->button()
                                    ->color('success')
                                    ->disabled(fn() => !$this->selectedUserId || !$this->selectedAgentId)
                                    ->action(function () {
                                        $this->createChatSession();
                                        $this->dispatch('refresh-simulation-log', simulationLog: $this->simulationLog);
                                        // $this->dispatch('refresh-status', [
                                        //     'userPoints' => $this->userPoints,
                                        //     'agentPointsRequired' => $this->agentPointsRequired,
                                        //     'chatSessionId' => $this->chatSessionId,
                                        //     'userRenewalInfo' => $this->userRenewalInfo,
                                        // ]);
                                    }),

                                Action::make('send_message')
                                    ->label('Send New Message')
                                    ->button()
                                    ->color('primary')
                                    ->disabled(fn() => !$this->chatSessionId)
                                    ->action(function () {
                                        $this->sendChatMessage();
                                        $this->dispatch('refresh-simulation-log', simulationLog: $this->simulationLog);
                                        // $this->dispatch('refresh-status', [
                                        //     'userPoints' => $this->userPoints,
                                        //     'agentPointsRequired' => $this->agentPointsRequired,
                                        //     'chatSessionId' => $this->chatSessionId,
                                        //     'userRenewalInfo' => $this->userRenewalInfo,
                                        // ]);
                                    }),
                            ]),
                        ]),
                    ]),

                Section::make('Simulation Log')
                    ->schema([
                        ViewField::make('simulation_log')
                            ->view('filament.pages.partials.simulation-log')
                            ->viewData([
                                'simulationLog' => $this->simulationLog,
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    protected function updateUserPoints(): void
    {
        if ($this->selectedUserId) {
            $user = User::find($this->selectedUserId);
            if ($user) {
                $this->userPoints = $user->points;
                $this->isGuestUser = $user->is_anonymous;
                $hasActiveSubscription = $user->hasActiveSubscription();

                // Reset daily points info
                $this->dailyPointsLimit = null;
                $this->dailyPointsUsed = null;
                $this->isUsingDowngradedPrompt = false;

                // Check if user has active subscription and update daily points info
                if ($hasActiveSubscription) {
                    $this->dailyPointsLimit = (new SubscriptionPointsService())->getDailyPointsLimit($user);
                    $this->dailyPointsUsed = (new SubscriptionPointsService())->getPointsUsedToday($user);
                    $this->isUsingDowngradedPrompt = (new SubscriptionPointsService())->hasExceededDailyLimit($user);
                }

                if ($user->is_anonymous && $user->next_points_renewal_at) {
                    // Guest user with renewal date
                    $daysUntilRenewal = round(now()->diffInDays($user->next_points_renewal_at, false));

                    if ($daysUntilRenewal > 0) {
                        $this->userRenewalInfo = "Points will be renewed in {$daysUntilRenewal} days";
                    } elseif ($daysUntilRenewal == 0) {
                        $this->userRenewalInfo = "Points will be renewed today";
                    } else {
                        $this->userRenewalInfo = "Points should be renewed soon";
                    }
                } elseif (!$user->is_anonymous && !$hasActiveSubscription && $user->next_points_renewal_at) {
                    // Registered user without subscription but with renewal date
                    $hoursUntilRenewal = round(now()->diffInHours($user->next_points_renewal_at, false));

                    if ($hoursUntilRenewal > 0) {
                        $this->userRenewalInfo = "Points will be renewed in {$hoursUntilRenewal} hours";
                    } elseif ($hoursUntilRenewal == 0) {
                        $this->userRenewalInfo = "Points will be renewed within the next hour";
                    } else {
                        $this->userRenewalInfo = "Points should be renewed soon";
                    }
                } else {
                    // Default cases
                    if ($user->is_anonymous) {
                        $this->userRenewalInfo = "Guest user - points renew every " . SystemSetting::getValue(SystemSetting::KEY_GUEST_POINTS_RENEWAL_DAYS, 7) . " days";
                    } elseif (!$hasActiveSubscription) {
                        $this->userRenewalInfo = "Regular user - points renew every " . SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_HOURS, 24) . " hours";
                    } else {
                        if ($this->isUsingDowngradedPrompt) {
                            $this->userRenewalInfo = "Subscribed user - daily limit exceeded, using downgraded prompt";
                        } else {
                            $this->userRenewalInfo = "Subscribed user - {$this->dailyPointsUsed}/{$this->dailyPointsLimit} daily points used";
                        }
                    }
                }
            } else {
                $this->userPoints = 0;
                $this->isGuestUser = false;
                $this->userRenewalInfo = null;
                $this->dailyPointsLimit = null;
                $this->dailyPointsUsed = null;
                $this->isUsingDowngradedPrompt = false;
            }
        } else {
            $this->userPoints = 0;
            $this->isGuestUser = false;
            $this->userRenewalInfo = null;
            $this->dailyPointsLimit = null;
            $this->dailyPointsUsed = null;
            $this->isUsingDowngradedPrompt = false;
        }
    }

    protected function updateAgentPointsRequired(): void
    {
        if ($this->selectedAgentId) {
            $agent = Agent::find($this->selectedAgentId);
            $this->agentPointsRequired = $agent ? $agent->points_required : 0;
        } else {
            $this->agentPointsRequired = 0;
        }
    }

    public function createChatSession(): void
    {
        $user = User::find($this->selectedUserId);
        $agent = Agent::find($this->selectedAgentId);

        if (!$user || !$agent) {
            Notification::make()
                ->title('Error')
                ->body('User or Agent not found')
                ->danger()
                ->send();
            return;
        }

        // Check if user has sufficient points
        if ($user->points < $agent->points_required) {
            $this->addToLog('error', "Insufficient points to start a conversation with this agent. Required: {$agent->points_required}, Available: {$user->points}");

            // Check user type and subscription status
            if ($user->is_anonymous) {
                // Guest user
                $renewalMessage = $this->getGuestRenewalMessage($user);
                $this->addToLog('warning', $renewalMessage);

                Notification::make()
                    ->title('Insufficient Points')
                    ->body("User needs {$agent->points_required} points but only has {$user->points}. {$renewalMessage}")
                    ->danger()
                    ->send();
                return;
            } elseif (!$user->hasActiveSubscription()) {
                // Registered user without active subscription
                $renewalMessage = $this->getRegisteredUserRenewalMessage($user);
                $this->addToLog('warning', $renewalMessage);

                Notification::make()
                    ->title('Insufficient Points')
                    ->body("User needs {$agent->points_required} points but only has {$user->points}. {$renewalMessage}")
                    ->danger()
                    ->send();
                return;
            } else {
                // Registered user with active subscription
                // Allow them to continue with downgraded prompt
                $this->isUsingDowngradedPrompt = true;

                if ($agent->downgrade_prompt_id) {
                    $this->addToLog('warning', "Insufficient points - using downgraded prompt for subscribed user");

                    Notification::make()
                        ->title('Using Downgraded Prompt')
                        ->body("User has insufficient points. Creating session with downgraded prompt without point deduction.")
                        ->warning()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Insufficient Points')
                        ->body("User needs {$agent->points_required} points but only has {$user->points}")
                        ->danger()
                        ->send();
                    return;
                }
            }
        }

        // Create chat session
        $chatSession = ChatSession::create([
            'user_id' => $user->id,
            'agent_id' => $agent->id,
            'status' => 'active',
            'last_message_at' => now(),
        ]);

        $this->chatSessionId = $chatSession->id;

        // Deduct points if not using downgraded prompt
        if ($user->hasActiveSubscription() && $this->isUsingDowngradedPrompt) {
            $this->addToLog('success', "Created chat session #{$chatSession->id} with downgraded prompt (no points deducted)");

            Notification::make()
                ->title('Chat Session Created')
                ->body("Session #{$chatSession->id} created with downgraded prompt (no points deducted)")
                ->success()
                ->send();
        } else {
            // Deduct points for regular users or subscribed users who haven't exceeded their limit
            $user->deductSomePoints($agent->points_required, "Points used to start chat session with {$agent->name}", Auth::id());
            $this->addToLog('success', "Created chat session #{$chatSession->id} and deducted {$agent->points_required} points");

            Notification::make()
                ->title('Chat Session Created')
                ->body("Session #{$chatSession->id} created and {$agent->points_required} points deducted")
                ->success()
                ->send();
        }

        // Update user points display
        $this->updateUserPoints();
    }

    protected function generateUserMessage(): string
    {
        $userMessages = [
            "Hello, how are you today?",
            "Can you help me with something?",
            "I'm looking for information about this topic.",
            "What do you think about this idea?",
            "I need some advice on a decision I'm making.",
            "Tell me more about your capabilities.",
            "How does this work exactly?",
            "I'm curious about your opinion on this matter.",
            "Can you explain this concept to me?",
            "What would you recommend in this situation?"
        ];

        return $userMessages[array_rand($userMessages)];
    }

    protected function generateAgentMessage(string $agentName): string
    {
        $agentMessages = [
            "I'm doing well, thank you for asking! How can I assist you today?",
            "I'd be happy to help you with that. What specifically do you need assistance with?",
            "Here's some information about that topic: [detailed explanation would go here].",
            "That's an interesting idea. Have you considered these aspects as well?",
            "Based on what you've shared, I would suggest considering these options...",
            "I'm designed to assist with a wide range of topics and questions. What can I help you with?",
            "Let me explain how this works in more detail so you have a better understanding.",
            "From my perspective, there are several important factors to consider here.",
            "This concept can be understood as [detailed explanation]. Does that help clarify things?",
            "Based on the information provided, I would recommend the following approach..."
        ];

        return str_replace('[Agent]', $agentName, $agentMessages[array_rand($agentMessages)]);
    }

    /**
     * Get the renewal message for a guest user with insufficient points
     */
    protected function getGuestRenewalMessage(User $user): string
    {
        $renewalDays = SystemSetting::getValue(SystemSetting::KEY_GUEST_POINTS_RENEWAL_DAYS, 7);

        if ($user->next_points_renewal_at) {
            $daysUntilRenewal = round(now()->diffInDays($user->next_points_renewal_at, false));

            if ($daysUntilRenewal > 0) {
                return "Your points will be renewed in {$daysUntilRenewal} days. Sign up for a full account to get more points immediately!";
            } elseif ($daysUntilRenewal == 0) {
                return "Your points will be renewed today. Sign up for a full account to get more points immediately!";
            } else {
                return "Your points should be renewed soon. Sign up for a full account to get more points immediately!";
            }
        } else {
            return "Guest users get points renewed every {$renewalDays} days. Sign up for a full account to get more points immediately!";
        }
    }

    /**
     * Get the renewal message for a registered user without an active subscription
     */
    protected function getRegisteredUserRenewalMessage(User $user): string
    {
        $renewalHours = SystemSetting::getValue(SystemSetting::KEY_REGISTERED_POINTS_RENEWAL_HOURS, 24);

        if ($user->next_points_renewal_at) {
            $hoursUntilRenewal = round(now()->diffInHours($user->next_points_renewal_at, false));

            if ($hoursUntilRenewal > 0) {
                return "Your points will be renewed in {$hoursUntilRenewal} hours. Upgrade to Rydo+ to get more points immediately!";
            } elseif ($hoursUntilRenewal == 0) {
                return "Your points will be renewed within the next hour. Upgrade to Rydo+ to get more points immediately!";
            } else {
                return "Your points should be renewed soon. Upgrade to Rydo+ to get more points immediately!";
            }
        } else {
            return "Registered users get points renewed every {$renewalHours} hours. Upgrade to Rydo+ to get more points immediately!";
        }
    }

    public function sendChatMessage(): void
    {
        $chatSession = ChatSession::find($this->chatSessionId);

        if (!$chatSession) {
            Notification::make()
                ->title('Error')
                ->body('Chat session not found')
                ->danger()
                ->send();
            return;
        }

        $user = User::find($this->selectedUserId);
        $agent = Agent::find($this->selectedAgentId);

        if (!$user || !$agent) {
            Notification::make()
                ->title('Error')
                ->body('User or Agent not found')
                ->danger()
                ->send();
            return;
        }

        // Generate random user message
        $userMessageText = $this->generateUserMessage();

        // Create user message
        $userMessage = ChatMessage::create([
            'chat_session_id' => $chatSession->id,
            'user_id' => $user->id,
            'agent_id' => $agent->id,
            'prompt_id' => $agent->prompt_id,
            'sender' => 'user',
            'message_text' => $userMessageText,
            'points_used' => 0, // User messages don't use points
        ]);

        $this->addToLog('info', "User: {$userMessageText}");

        // Generate random agent response
        $agentMessageText = $this->generateAgentMessage($agent->name);

        // Determine if we should deduct points
        $shouldDeductPoints = false;
        $pointsUsed = 0;

        if ($user->hasActiveSubscription()) {
            // For subscribed users, check if they've exceeded their daily limit
            if ($this->isUsingDowngradedPrompt) {
                // If using downgraded prompt, don't deduct points
                $shouldDeductPoints = false;
                $pointsUsed = 0;
                $this->addToLog('info', "Subscribed user has exceeded daily limit - using downgraded prompt without deducting points");
            } else {
                // If not using downgraded prompt, randomly decide whether to deduct points (70% chance)
                $shouldDeductPoints = rand(1, 10) <= 7;
                $pointsUsed = $shouldDeductPoints ? $agent->points_required : 0;
            }
        } else {
            // For non-subscribed users, randomly decide whether to deduct points (70% chance)
            $shouldDeductPoints = rand(1, 10) <= 7;
            $pointsUsed = $shouldDeductPoints ? $agent->points_required : 0;
        }

        $shouldDeductPointsString = $shouldDeductPoints ? 'Yes' : 'No';

        // Check if we should use the downgraded prompt for subscribed users who exceeded daily limit
        $promptId = $agent->prompt_id;
        if ($user->hasActiveSubscription() && $this->isUsingDowngradedPrompt && $agent->downgrade_prompt_id) {
            $promptId = $agent->downgrade_prompt_id;
            $this->addToLog('warning', "Using downgraded prompt (ID: {$promptId}) due to daily points limit exceeded");

            // Force no points deduction when using downgraded prompt
            $shouldDeductPoints = false;
            $pointsUsed = 0;
        }

        // Create agent response
        $agentMessage = ChatMessage::create([
            'chat_session_id' => $chatSession->id,
            'user_id' => $user->id,
            'agent_id' => $agent->id,
            'prompt_id' => $promptId,
            'sender' => 'agent',
            'message_text' => $agentMessageText,
            'points_used' => $pointsUsed,
        ]);

        $this->addToLog('info', "Agent: {$agentMessageText} - Should deduct points: {$shouldDeductPointsString}");

        // Deduct points for agent response if needed
        if ($shouldDeductPoints) {
            // Check if user has sufficient points
            if ($user->points < $agent->points_required) {
                $this->addToLog('error', "Insufficient points to deduct. Required: {$agent->points_required}, Available: {$user->points}");

                // Check user type and subscription status
                if ($user->is_anonymous) {
                    // Guest user
                    $renewalMessage = $this->getGuestRenewalMessage($user);
                    $this->addToLog('warning', $renewalMessage);

                    Notification::make()
                        ->title('Insufficient Points')
                        ->body("User needs {$agent->points_required} points but only has {$user->points}. {$renewalMessage}")
                        ->danger()
                        ->send();
                } elseif (!$user->hasActiveSubscription()) {
                    // Registered user without active subscription
                    $renewalMessage = $this->getRegisteredUserRenewalMessage($user);
                    $this->addToLog('warning', $renewalMessage);

                    Notification::make()
                        ->title('Insufficient Points')
                        ->body("User needs {$agent->points_required} points but only has {$user->points}. {$renewalMessage}")
                        ->danger()
                        ->send();
                } else {
                    // Registered user with active subscription
                    if ($this->isUsingDowngradedPrompt) {
                        // If already using downgraded prompt, just inform that they're out of points
                        Notification::make()
                            ->title('Using Downgraded Prompt')
                            ->body("User is out of points but can continue using the downgraded prompt without point deduction.")
                            ->warning()
                            ->send();
                    } else {
                        // If not yet using downgraded prompt, inform that they'll be switched to it
                        Notification::make()
                            ->title('Insufficient Points')
                            ->body("User needs {$agent->points_required} points but only has {$user->points}. Switching to downgraded prompt.")
                            ->warning()
                            ->send();

                        // Mark as using downgraded prompt
                        $this->isUsingDowngradedPrompt = true;
                    }
                }

                // Still allow the message to be sent, but don't deduct points
                $this->addToLog('warning', "Message sent but no points were deducted due to insufficient balance.");
            } else {
                $user->deductSomePoints($agent->points_required, "Points used for message from {$agent->name}", Auth::id());
                $this->addToLog('success', "Deducted {$agent->points_required} points from user for agent response");
            }
        } else {
            $this->addToLog('info', "No points deducted for this message (random chance).");
        }

        // Update chat session last message time
        $chatSession->update(['last_message_at' => now()]);

        // Update user points display
        $this->updateUserPoints();

        $this->addToLog('success', "Sent message in chat session #{$chatSession->id}");
        $this->addToLog('info', "User now has {$user->points} points");

        if ($user->hasActiveSubscription() && $this->isUsingDowngradedPrompt) {
            Notification::make()
                ->title('Message Sent')
                ->body("Using downgraded prompt - no points deducted")
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Message Sent')
                ->body($shouldDeductPoints ? "{$agent->points_required} points deducted for agent response" : "No points deducted for this message")
                ->success()
                ->send();
        }


        $this->addToLog('info', "--------------------------------------------------");
    }

    protected function addToLog(string $type, string $message): void
    {
        $this->simulationLog[] = [
            'type' => $type,
            'message' => $message,
            'time' => now()->format('H:i:s'),
        ];
    }
}
