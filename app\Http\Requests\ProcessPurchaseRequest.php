<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProcessPurchaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by auth:sanctum middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'platform' => 'required|string|in:ios,android',
            'product_id' => 'required|string',
            'receipt_data' => 'required|string',
            'transaction_id' => 'required|string',
            'is_reactivation' => 'boolean',
            'source_screen' => 'nullable|string',
            'idempotency_key' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'platform.required' => 'Platform is required.',
            'platform.in' => 'Platform must be either ios or android.',
            'product_id.required' => 'Product ID is required.',
            'receipt_data.required' => 'Receipt data is required.',
            'transaction_id.required' => 'Transaction ID is required.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_reactivation' => $this->boolean('is_reactivation', false),
        ]);
    }
}
