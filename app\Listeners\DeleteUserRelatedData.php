<?php

namespace App\Listeners;

use App\Events\UserDeleted;
use App\Models\Message;
use App\Models\MessageReaction;
use App\Models\MessageReport;
use App\Models\PointAdjustment;
use App\Models\Recommendation;
use App\Models\SocialProvider;
use App\Models\Subscription;
use App\Models\UserRecommendationUsage;
use Illuminate\Support\Facades\DB;

class DeleteUserRelatedData
{
    public function handle(UserDeleted $event): void
    {
        $user = $event->user;
        $adminId = $event->adminId;

        DB::transaction(function () use ($user, $adminId) {
            // Soft delete messages
            // Message::where('user_id', $user->id)->delete();

            // // Soft delete message reactions
            // MessageReaction::where('user_id', $user->id)->delete();

            // // Soft delete message reports
            // MessageReport::where('user_id', $user->id)->delete();

            // // Soft delete point adjustments
            // PointAdjustment::where('user_id', $user->id)->delete();

            // // Soft delete recommendations
            // Recommendation::where('user_id', $user->id)->delete();

            // Soft delete social providers
            SocialProvider::where('user_id', $user->id)->delete();

            // Soft delete subscriptions
            // Subscription::where('user_id', $user->id)->delete();

            // Soft delete user recommendation usage
            UserRecommendationUsage::where('user_id', $user->id)->delete();
        });
    }
} 