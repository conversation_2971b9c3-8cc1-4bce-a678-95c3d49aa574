<?php

namespace App\Filament\Resources\TrendingPlaceResource\Pages;

use App\Filament\Resources\TrendingPlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateTrendingPlace extends CreateRecord
{
    protected static string $resource = TrendingPlaceResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['is_trending'] = true;
        
        return $data;
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
