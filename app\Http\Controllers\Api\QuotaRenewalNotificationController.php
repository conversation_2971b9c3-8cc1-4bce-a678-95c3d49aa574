<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class QuotaRenewalNotificationController extends Controller
{
    /**
     * Mark the quota renewal notification as seen for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function markAsSeen(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Mark the notification as seen
        $user->markQuotaRenewalAsNotified();
        
        return response()->json([
            'message' => 'Quota renewal notification marked as seen',
            'success' => true,
        ]);
    }
}
