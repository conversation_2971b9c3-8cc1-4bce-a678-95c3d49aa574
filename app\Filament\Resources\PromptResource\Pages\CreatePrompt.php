<?php

namespace App\Filament\Resources\PromptResource\Pages;

use App\Filament\Resources\PromptResource;
use App\Models\Agent;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreatePrompt extends CreateRecord
{
    protected static string $resource = PromptResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function handleRecordCreation(array $data): Model
    {
        // Create the prompt record with the form data
        $record = static::getModel()::create($data);

        // Get the selected agent IDs from the form
        $agentIds = $this->data['agent_id'] ?? [];

        // Update the agents that use this prompt
        if (!empty($agentIds)) {
            // For each selected agent, set its prompt_id to this prompt
            Agent::whereIn('id', $agentIds)
                ->update(['prompt_id' => $record->id]);
        }

        return $record;
    }
}
