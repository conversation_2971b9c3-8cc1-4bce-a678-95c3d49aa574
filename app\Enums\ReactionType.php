<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum ReactionType: string implements HasColor, HasLabel
{
    case LIKE = 'like';
    case DISLIKE = 'dislike';
    case COPY = 'copy';

    public function icon(): string
    {
        return match($this) {
            self::LIKE => 'heroicon-m-hand-thumb-up',
            self::DISLIKE => 'heroicon-m-hand-thumb-down',
            self::COPY => 'heroicon-m-document-duplicate',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::LIKE => 'success',
            self::DISLIKE => 'danger',
            self::COPY => 'primary',
        };
    }

    public function getLabel(): ?string
    {
        return match ($this) {
            self::LIKE => 'Like',
            self::DISLIKE => 'Dislike',
            self::COPY => 'Copy',
        };
    }
} 