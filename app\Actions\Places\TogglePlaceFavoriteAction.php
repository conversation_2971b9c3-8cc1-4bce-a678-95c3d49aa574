<?php

namespace App\Actions\Places;

use App\Enums\FavoriteType;
use App\Models\Place;
use App\Models\PlaceFavorite;
use App\Models\User;

class TogglePlaceFavoriteAction
{
    /**
     * Toggle favorite status for a place
     *
     * @param User $user
     * @param Place $place
     * @param FavoriteType|null $favoriteType
     * @return bool
     */
    /** @todo need to do refactor and also need to enhance data tracking for favorite */
    public function execute(User $user, Place $place, ?FavoriteType $favoriteType = null): bool
    {
        $favorite = PlaceFavorite::query()->where('user_id', $user->id)
            ->where('place_id', $place->id)
            ->first();

        if ($favorite) {
            if ($favorite->type === $favoriteType || $favoriteType === null) {
                $favorite->delete();
                return false;
            } else {
                $favorite->type = $favoriteType;
                $favorite->save();
                return true;
            }
        } else {
            PlaceFavorite::create([
                'user_id' => $user->id,
                'place_id' => $place->id,
                'type' => $favoriteType ?? FavoriteType::PLANNED,
            ]);
            return true;
        }
    }
}
