<?php

namespace App\Filament\Resources\PlaceResource\Pages;

use App\Filament\Resources\PlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Filament\Infolists\Components\IconEntry;
// use Cheesegrits\FilamentGoogleMaps\Infolists\MapEntry;

class ViewPlace extends ViewRecord
{
    protected static string $resource = PlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Basic Information')
                    ->schema([
                        TextEntry::make('name')  
                             ->limit(50)
                            ->label('Name'),
                        TextEntry::make('brief_description')
                            ->limit(50)
                            ->label('Brief Description'),
                        TextEntry::make('description') 
                            ->limit(50)   
                            ->markdown()
                            ->columnSpanFull(),
                        TextEntry::make('rating')
                            ->numeric(decimalPlaces: 1)
                            ->suffix(' / 5.0'),
                    ])
                    ->columns(2),

                Section::make('Location Details')
                    ->schema([
                        TextEntry::make('place_id')
                            ->label('Place ID'),
                        TextEntry::make('address.en')
                            ->label('Address'),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('latitude')
                                    ->numeric(decimalPlaces: 8),
                                TextEntry::make('longitude')
                                    ->numeric(decimalPlaces: 8),
                            ]),
                    ])
                    ->columns(2),

                Section::make('Categorization')
                    ->schema([
                        TextEntry::make('category.name')
                            ->label('Category'),
                        TextEntry::make('agent.name')
                            ->label('Agent'),
                        TextEntry::make('persona')
                            ->badge()
                            ->color(fn ($state) => $state?->getColor()),
                    ])
                    ->columns(2),

                Section::make('Status Information')
                    ->schema([
                        IconEntry::make('is_active')
                            ->label('Active')
                            ->boolean(),
                        IconEntry::make('is_choice')
                            ->label('Rydo Choice')
                            ->boolean(),
                        IconEntry::make('is_trending')
                            ->label('Trending')
                            ->boolean(),
                        TextEntry::make('source')
                            ->badge(),
                    ])
                    ->columns(4),

                Section::make('Contact Information')
                    ->schema([
                        TextEntry::make('website')
                            ->label('Website')
                            ->url(fn ($state): ?string => $state ? $state : null),
                        TextEntry::make('phone_number')
                            ->label('Phone Number'),
                    ])
                    ->columns(2),

                Section::make('Media Gallery')
                    ->schema([
                        SpatieMediaLibraryImageEntry::make('place_photos')
                            ->label('Photos')
                            ->collection('place_photos')
                            ->columnSpanFull(),
                    ]),

                Section::make('Favorites Information')
                    ->schema([
                        TextEntry::make('favorites_count')
                            ->label('Total Favorites')
                            ->state(function ($record) {
                                return $record->favorites()->count();
                            }),
                        TextEntry::make('planned_favorites')
                            ->label('Planned Visits')
                            ->state(function ($record) {
                                return $record->favorites()->where('type', 'planned')->count();
                            }),
                        TextEntry::make('visited_favorites')
                            ->label('Visited')
                            ->state(function ($record) {
                                return $record->favorites()->where('type', 'visited')->count();
                            }),
                    ])
                    ->columns(3),

                Section::make('Timestamps')
                    ->schema([
                        TextEntry::make('created_at')
                            ->dateTime(),
                        TextEntry::make('updated_at')
                            ->dateTime(),
                    ])
                    ->columns(2)
                    ->collapsed(),
            ]);
    }
}
