<?php

namespace App\Filament\Resources\ChatSessionResource\RelationManagers;

use App\Enums\ChatMessageSender;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MessagesRelationManager extends RelationManager
{
    protected static string $relationship = 'messages';

    protected static ?string $recordTitleAttribute = 'message_text';

    protected static ?string $title = 'Chat Messages';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('sender')
                    ->options([
                        ChatMessageSender::USER->value => 'User',
                        ChatMessageSender::AGENT->value => 'Agent',
                    ])
                    ->required(),
                Forms\Components\Textarea::make('message_text')
                    ->required()
                    ->maxLength(65535)
                    ->columnSpanFull(),
                // Forms\Components\TextInput::make('points_used')
                //     ->numeric()
                //     ->minValue(0)
                //     ->maxValue(999999)
                //     ->required()
                //     ->default(0),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('message_text')
            ->columns([
                Tables\Columns\TextColumn::make('sender')
                    ->badge()
                    ->formatStateUsing(fn ($state) => $state->value ?? $state)
                    ->color(fn ($state): string => match (is_string($state) ? $state : ($state->value ?? 'unknown')) {
                        'user' => 'primary',
                        'agent' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('message_text')
                    ->label('Message')
                    ->wrap()
                    ->searchable()
                    ->html()
                    ->formatStateUsing(
                        fn(string $state, $record): string =>
                        sprintf(
                            '<div class="text-%s-600">%s</div>',
                            (is_string($record->sender) ? $record->sender : $record->sender->value) === 'user' ? 'primary' : 'success',
                            nl2br(e($state))
                        )
                    ),
                Tables\Columns\TextColumn::make('points_used')
                    ->label('Points')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('sender')
                    ->options([
                        ChatMessageSender::USER->value => 'User',
                        ChatMessageSender::AGENT->value => 'Agent',
                    ]),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'asc');
    }
}
