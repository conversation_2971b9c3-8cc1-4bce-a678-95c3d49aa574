<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('chat_messages')
            ->whereIn('id', function ($query) {
                $query->select('chat_message_id')
                    ->from('recommendations')
                    ->whereNotNull('chat_message_id');
            })
            ->update(['is_recommended' => true]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('chat_messages')
            ->whereIn('id', function ($query) {
                $query->select('chat_message_id')
                    ->from('recommendations')
                    ->whereNotNull('chat_message_id');
            })
            ->update(['is_recommended' => false]);
    }
}; 