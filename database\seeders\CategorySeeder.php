<?php

namespace Database\Seeders;

use App\Models\Agent;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if command is available (when run from console)
        if (isset($this->command)) {
            $this->command->info('Truncating categories table...');
        }

        $this->truncateCategoriesTable();

        // Define categories for each agent
        $agentCategories = [
            // Adventures Assistant (agent_id=2)
            2 => [
                [
                    'name' => 'Parks & Gardens',
                    'slug' => 'parks-gardens',
                    'description' => 'Beautiful parks and gardens for relaxation and outdoor activities.',
                ],
                [
                    'name' => 'Tourist Attractions',
                    'slug' => 'tourist-attractions',
                    'description' => 'Popular tourist destinations and attractions.',
                ],
                [
                    'name' => 'Beaches & Nature',
                    'slug' => 'beaches-nature',
                    'description' => 'Natural beaches and scenic outdoor locations.',
                ],
                [
                    'name' => 'Museums & Culture',
                    'slug' => 'museums-culture',
                    'description' => 'Museums and cultural sites for educational experiences.',
                ],
                [
                    'name' => 'Zoos & Aquariums',
                    'slug' => 'zoos-aquariums',
                    'description' => 'Zoos and aquariums featuring wildlife and marine life.',
                ],
                [
                    'name' => 'Theme Parks',
                    'slug' => 'theme-parks',
                    'description' => 'Amusement and theme parks for entertainment.',
                ],
                [
                    'name' => 'Outdoor Events',
                    'slug' => 'outdoor-events',
                    'description' => 'Outdoor events and activities.',
                ],
            ],

            // Bites Assistant (agent_id=3)
            3 => [
                [
                    'name' => 'Restaurants',
                    'slug' => 'restaurants',
                    'description' => 'Various restaurants offering different cuisines.',
                ],
                [
                    'name' => 'Cafés & Bakeries',
                    'slug' => 'cafes-bakeries',
                    'description' => 'Cafés and bakeries for coffee, pastries, and light meals.',
                ],
                [
                    'name' => 'Takeaway & Delivery',
                    'slug' => 'takeaway-delivery',
                    'description' => 'Food establishments offering takeaway and delivery services.',
                ],
                [
                    'name' => 'Street Food',
                    'slug' => 'street-food',
                    'description' => 'Street food vendors and casual dining options.',
                ],
                [
                    'name' => 'Trending Spots',
                    'slug' => 'trending-spots',
                    'description' => 'Popular and trending food destinations.',
                ],
            ],

            // Needs Assistant (agent_id=4)
            4 => [
                [
                    'name' => 'Pharmacies',
                    'slug' => 'pharmacies',
                    'description' => 'Pharmacies and drugstores for medical supplies.',
                ],
                [
                    'name' => 'Clinics & Hospitals',
                    'slug' => 'clinics-hospitals',
                    'description' => 'Medical clinics and hospitals for healthcare services.',
                ],
                [
                    'name' => 'Salons & Barbers',
                    'slug' => 'salons-barbers',
                    'description' => 'Hair salons, beauty salons, and barber shops.',
                ],
                [
                    'name' => 'Supermarkets & Groceries',
                    'slug' => 'supermarkets-groceries',
                    'description' => 'Supermarkets and grocery stores for daily essentials.',
                ],
                [
                    'name' => 'Laundry & Cleaning',
                    'slug' => 'laundry-cleaning',
                    'description' => 'Laundry services and cleaning establishments.',
                ],
                [
                    'name' => 'Pet Care & Stores',
                    'slug' => 'pet-care-stores',
                    'description' => 'Pet stores and veterinary services.',
                ],
            ],

            // Ventures Assistant (agent_id=5)
            5 => [
                [
                    'name' => 'Coworking Spaces',
                    'slug' => 'coworking-spaces',
                    'description' => 'Shared workspaces for professionals and freelancers.',
                ],
                [
                    'name' => 'Meeting Rooms',
                    'slug' => 'meeting-rooms',
                    'description' => 'Spaces for business meetings and conferences.',
                ],
                [
                    'name' => 'Study Spots',
                    'slug' => 'study-spots',
                    'description' => 'Quiet places suitable for studying and focused work.',
                ],
                [
                    'name' => 'Printing & Business Services',
                    'slug' => 'printing-business-services',
                    'description' => 'Printing shops and business service providers.',
                ],
                [
                    'name' => 'Startup Hubs & Incubators',
                    'slug' => 'startup-hubs-incubators',
                    'description' => 'Hubs and incubators for startups and entrepreneurs.',
                ],
            ],
        ];

        // Create categories for each agent
        foreach ($agentCategories as $agentId => $categories) {
            $agent = Agent::find($agentId);

            if (!$agent) {
                if (isset($this->command)) {
                    $this->command->error("Agent with ID {$agentId} not found. Skipping categories for this agent.");
                }
                continue;
            }

            if (isset($this->command)) {
                $this->command->info("Creating categories for agent: {$agent->name}");
            }

            foreach ($categories as $categoryData) {
                $category = Category::create([
                    'name' => $categoryData['name'],
                    'slug' => $categoryData['slug'],
                    'description' => $categoryData['description'],
                    'agent_id' => $agentId,
                    'is_active' => true,
                ]);

                if (isset($this->command)) {
                    $this->command->info("Created category: {$category->name}");
                }
            }
        }

        if (isset($this->command)) {
            $this->command->info('Categories seeding completed successfully.');
        }
    }

    /**
     * Truncate the categories table
     */
    private function truncateCategoriesTable(): void
    {
        // Determine the database connection type
        $connection = DB::connection()->getDriverName();

        if ($connection === 'mysql') {
            // MySQL-specific approach
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            DB::table('categories')->truncate();
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        } elseif ($connection === 'pgsql') {
            // PostgreSQL-specific approach
            DB::statement('TRUNCATE TABLE categories CASCADE;');
        } else {
            // Generic approach for other databases
            Category::query()->delete();
        }

        if (isset($this->command)) {
            $this->command->info('Categories table truncated successfully.');
        }
    }
}
