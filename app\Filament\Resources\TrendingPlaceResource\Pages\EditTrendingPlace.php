<?php

namespace App\Filament\Resources\TrendingPlaceResource\Pages;

use App\Filament\Resources\TrendingPlaceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTrendingPlace extends EditRecord
{
    protected static string $resource = TrendingPlaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('removeFromTrending')
                ->label('Remove from Trending')
                ->color('danger')
                ->icon('heroicon-o-x-mark')
                ->action(function () {
                    $this->record->update(['is_trending' => false]);
                    $this->redirect(TrendingPlaceResource::getUrl());
                }),
        ];
    }
    
    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['is_trending'] = true;
        
        return $data;
    }
}
