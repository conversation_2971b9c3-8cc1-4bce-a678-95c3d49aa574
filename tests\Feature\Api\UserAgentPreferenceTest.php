<?php

namespace Tests\Feature\Api;

use App\Enums\UserType;
use App\Models\Agent;
use App\Models\User;
use App\Models\UserAgentPreference;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserAgentPreferenceTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Agent $agent;

    protected function setUp(): void
    {
        parent::setUp();
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => 'password',
            'user_type' => UserType::Admin,
        ]);
        // Create a user and an agent for testing
        $this->user = User::factory()->create();
        $this->agent = Agent::factory()->create([
            'is_visible' => true,
            'is_general' => false,
        ]);
    }

    /** @test */
    public function unauthenticated_users_cannot_access_user_agent_preferences_endpoint()
    {
        $response = $this->postJson('/api/users/interests', [
            'agent_id' => $this->agent->id,
            'interest_level' => 3,
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function authenticated_users_can_store_preferences()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'agent_id' => $this->agent->id,
                'interest_level' => 3,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'user_id',
                        'agent_id',
                        'interest_level',
                        'created_at',
                        'updated_at',
                    ]
                ]
            ]);

        $this->assertDatabaseHas('user_agent_preferences', [
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
            'interest_level' => 3,
        ]);
    }

    /** @test */
    public function authenticated_users_can_update_their_preferences()
    {
        // Create a preference first
        $preference = UserAgentPreference::factory()->create([
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
            'interest_level' => 3,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'agent_id' => $this->agent->id,
                'interest_level' => 4,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'user_id',
                        'agent_id',
                        'interest_level',
                        'created_at',
                        'updated_at',
                    ]
                ]
            ]);

        $this->assertDatabaseHas('user_agent_preferences', [
            'id' => $preference->id,
            'user_id' => $this->user->id,
            'agent_id' => $this->agent->id,
            'interest_level' => 4,
        ]);
    }

    /** @test */
    public function validation_fails_with_invalid_data()
    {
        // Test with missing agent_id
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'interest_level' => 3,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['agent_id']);

        // Test with invalid interest_level (too low)
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'agent_id' => $this->agent->id,
                'interest_level' => 0,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['interest_level']);

        // Test with invalid interest_level (too high)
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'agent_id' => $this->agent->id,
                'interest_level' => 6,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['interest_level']);

        // Test with non-existent agent_id
        $response = $this->actingAs($this->user)
            ->postJson('/api/users/interests', [
                'agent_id' => 999,
                'interest_level' => 3,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['agent_id']);
    }
}
